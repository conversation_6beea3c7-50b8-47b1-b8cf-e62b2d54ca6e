import { NextRequest, NextResponse } from "next/server";
import { verifyJwtAuth } from "@/lib/api-middleware";

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;

// knowledge:start remove deprecated restaurantDb import and usage
// import { restaurantDb } from '@/lib/db/restaurant-db';
// (remove all code that uses restaurantDb)
// knowledge:end remove deprecated restaurantDb import and usage
import {
  StaffMember,
  StaffDocument,
  StaffMetadata
} from '@/lib/types/unified-staff';

/**
 * Debug API endpoint to fix staff member IDs
 * This endpoint will:
 * 1. Get all staff members from the restaurant database
 * 2. For each staff member, check if there's a corresponding auth document
 * 3. If there is, update the staff member's userId to match the auth document ID
 * 4. If there isn't, create a new auth document with the same ID as the staff member
 */
export async function GET(request: NextRequest) {
  try {
    console.log("API: /api/staff/debug-fix-ids - Request received");

    // Verify JWT authentication
    console.log("API: Verifying JWT authentication");
    const authResult = await verifyJwtAuth(request);

    if (!authResult.success || !authResult.user) {
      console.error("API: JWT verification failed", {
        success: authResult.success,
        error: authResult.error,
        hasUser: !!authResult.user
      });
      return NextResponse.json(
        { error: authResult.error || "Unauthorized" },
        { status: 401 }
      );
    }

    // Log the authenticated user
    console.log("API: User authenticated", {
      id: authResult.user.id,
      role: authResult.user.role,
      restaurantId: authResult.user.restaurantId
    });

    // Verify that the user is authorized to debug staff records
    // Only restaurant owners or admins should be able to do this
    const { user } = authResult;

    if (user.role !== 'owner' && user.role !== 'admin') {
      console.error("API: Insufficient permissions", {
        userRole: user.role,
        requiredRoles: ['owner', 'admin'],
        userId: user.id
      });
      return NextResponse.json(
        { error: "Insufficient permissions to debug staff records" },
        { status: 403 }
      );
    }

    // Get the restaurant ID from the user
    // const restaurantId = serverCleanRestaurantId(user.restaurantId);
    // console.log(`API: Using restaurant ID: ${restaurantId}`);

    // TODO: Implement local/offline staff member retrieval and update logic here
    // Example: const staffMembers = await getAllStaffMembersLocal(user.restaurantId);
    const staffMembers: any[] = [];

    // Results tracking
    const results = {
      total: staffMembers.length,
      fixed: 0,
      alreadyConsistent: 0,
      errors: 0,
      details: [] as any[]
    };

    // Comment out the rest of the processing loop for now
    /*
    for (const staffMember of staffMembers) {
      // ...rest of the loop logic...
    }
    */

    // Return the results
    return NextResponse.json({
      success: true,
      results
    });
  } catch (error) {
    console.error("API: Error in debug-fix-ids:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
