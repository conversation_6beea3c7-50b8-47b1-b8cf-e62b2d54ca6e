import { NextRequest, NextResponse } from "next/server";
import { verifyJwtAuth } from "@/lib/api-middleware";
import { v4 as uuidv4 } from 'uuid';

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;

// import { createUser } from "@/lib/couchdb-auth";

export async function POST(request: NextRequest) {
  try {
    console.log("API: /api/staff/debug-auth - Request received");

    // Verify JWT authentication
    console.log("API: Verifying JWT authentication");
    const authResult = await verifyJwtAuth(request);

    if (!authResult.success || !authResult.user) {
      console.error("API: JWT verification failed", {
        success: authResult.success,
        error: authResult.error,
        hasUser: !!authResult.user,
        headers: Object.fromEntries(request.headers)
      });
      return NextResponse.json(
        { error: authResult.error || "Unauthorized" },
        { status: 401 }
      );
    }

    // Log the authenticated user
    console.log("API: User authenticated", {
      id: authResult.user.id,
      role: authResult.user.role,
      restaurantId: authResult.user.restaurantId
    });

    // Verify that the user is authorized to create staff accounts
    // Only restaurant owners or admins should be able to do this
    const { user } = authResult;

    if (user.role !== 'owner' && user.role !== 'admin') {
      console.error("API: Insufficient permissions", {
        userRole: user.role,
        requiredRoles: ['owner', 'admin'],
        userId: user.id
      });
      return NextResponse.json(
        { error: "Forbidden - requires admin or owner permissions" },
        { status: 403 }
      );
    }

    // Get restaurant ID from user
    const restaurantId = user.restaurantId;

    if (!restaurantId) {
      return NextResponse.json(
        { error: "Restaurant ID not found in user profile" },
        { status: 400 }
      );
    }

    // Generate a UUID for testing
    const testId = uuidv4();
    console.log(`API: Generated test UUID: ${testId}`);

    // TODO: Implement local/offline or MongoDB user creation logic here
    // const userResult = await createUser(...);
    const userResult = { success: false, userId: null, userDocId: null, error: 'Not implemented' };

    // Verify the user was created with the correct ID
    // TODO: Implement local/offline fetch logic here
    // const response = await fetch(...);

    return NextResponse.json({
      success: true,
      message: "Test user created and verified",
      testId,
      userId: userResult.userId,
      userDocId: userResult.userDocId,
      idsMatch: testId === userResult.userDocId
    });
  } catch (error) {
    console.error("API: Unexpected error in debug auth:", error);
    return NextResponse.json(
      { error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    );
  }
}
