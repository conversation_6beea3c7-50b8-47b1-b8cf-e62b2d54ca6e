import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/new-auth-service';
import { databaseV4 } from '@/lib/db/v4/core/db-instance';
import { validateSyncOperation } from '@/lib/sync/restaurant-validation';

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;


interface RouteParams {
  params: {
    deviceId: string;
    path: string[];
  };
}

async function getTargetDevice(deviceId: string, restaurantId: string) {
  const database = databaseV4.getDatabase();
  const collection = database.collection('sync_devices');
  
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  
  const device = await collection.findOne({
    deviceId,
    restaurantId,
    deviceType: 'desktop',
    status: 'active',
    lastSeen: { $gte: fiveMinutesAgo }
  });
  
  return device;
}

async function proxyRequest(req: NextRequest, targetUrl: string) {
  const headers = new Headers();
  
  // Copy relevant headers from the original request
  req.headers.forEach((value, key) => {
    if (key.toLowerCase() !== 'host' && 
        key.toLowerCase() !== 'authorization' &&
        !key.toLowerCase().startsWith('x-')) {
      headers.set(key, value);
    }
  });
  
  // Add basic auth for CouchDB
  headers.set('Authorization', 'Basic ' + Buffer.from('admin:admin').toString('base64'));
  
  const requestInit: RequestInit = {
    method: req.method,
    headers,
  };
  
  // Add body for POST, PUT, PATCH requests
  if (req.method !== 'GET' && req.method !== 'HEAD') {
    try {
      const body = await req.text();
      if (body) {
        requestInit.body = body;
      }
    } catch (error) {
      console.error('[Proxy] Error reading request body:', error);
    }
  }
  
  try {
    const response = await fetch(targetUrl, requestInit);
    
    const responseHeaders = new Headers();
    response.headers.forEach((value, key) => {
      if (key.toLowerCase() !== 'transfer-encoding' &&
          key.toLowerCase() !== 'connection' &&
          key.toLowerCase() !== 'keep-alive') {
        responseHeaders.set(key, value);
      }
    });
    
    // Add CORS headers
    responseHeaders.set('Access-Control-Allow-Origin', '*');
    responseHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    const responseBody = await response.text();
    
    return new NextResponse(responseBody, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });
    
  } catch (error) {
    console.error('[Proxy] Error proxying request:', error);
    return NextResponse.json({ 
      error: 'Proxy request failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { 
      status: 502,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

export async function GET(req: NextRequest, { params }: RouteParams) {
  return handleProxyRequest(req, params);
}

export async function POST(req: NextRequest, { params }: RouteParams) {
  return handleProxyRequest(req, params);
}

export async function PUT(req: NextRequest, { params }: RouteParams) {
  return handleProxyRequest(req, params);
}

export async function DELETE(req: NextRequest, { params }: RouteParams) {
  return handleProxyRequest(req, params);
}

export async function PATCH(req: NextRequest, { params }: RouteParams) {
  return handleProxyRequest(req, params);
}

async function handleProxyRequest(req: NextRequest, params: { deviceId: string; path: string[] }) {
  try {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = verifyToken(token);
    
    if (!decoded || !decoded.restaurantId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // CRITICAL: Validate sync operation to prevent cross-restaurant access
    const dbName = params.path[0]; // First path segment is the database name
    const syncValidation = validateSyncOperation({
      authToken: token,
      targetDeviceId: params.deviceId,
      requestedDbName: dbName,
      userRestaurantId: decoded.restaurantId
    });
    
    if (!syncValidation.isValid) {
      console.error('[Proxy] CRITICAL: Cross-restaurant sync attempt blocked!', {
        deviceId: params.deviceId,
        dbName,
        userRestaurantId: decoded.restaurantId,
        error: syncValidation.error,
        ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      });
      
      return NextResponse.json({ 
        error: 'Access denied - security validation failed'
      }, { status: 403 });
    }

    const { deviceId, path } = params;
    
    // Find the target desktop device
    const targetDevice = await getTargetDevice(deviceId, decoded.restaurantId);
    
    if (!targetDevice) {
      return NextResponse.json({ 
        error: 'Target device not found or offline',
        deviceId 
      }, { status: 404 });
    }
    
    // Use validated restaurant ID from security check
    const restaurantId = syncValidation.restaurantId!;
    const pathString = path.join('/');
    
    // Construct the target URL
    const targetUrl = `http://${targetDevice.ipAddress}:${targetDevice.couchdbPort}/${pathString}`;
    
    // Get query parameters from the original request
    const url = new URL(req.url);
    const queryString = url.search;
    const finalTargetUrl = targetUrl + queryString;
    
    console.log(`[Proxy] ${req.method} ${finalTargetUrl} (restaurant: ${restaurantId})`);
    
    return await proxyRequest(req, finalTargetUrl);
    
  } catch (error) {
    console.error('[Proxy] Error handling request:', error);
    return NextResponse.json({ 
      error: 'Proxy request failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

export async function OPTIONS(req: NextRequest, { params }: RouteParams) {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}