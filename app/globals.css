/* Font configuration for both Next.js fonts and static builds */

/* Local font definitions for static builds */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url('/fonts/inter.woff2') format('woff2');
}

@font-face {
  font-family: 'Almarai';
  font-style: normal;
  font-weight: 300 800;
  font-display: swap;
  src: url('/fonts/almarai.woff2') format('woff2');
}

@font-face {
  font-family: 'Tajawal';
  font-style: normal;
  font-weight: 300 900;
  font-display: swap;
  src: url('/fonts/tajawal.woff2') format('woff2');
}

@font-face {
  font-family: 'Changa';
  font-style: normal;
  font-weight: 300 800;
  font-display: swap;
  src: url('/fonts/changa.woff2') format('woff2');
}

/* Font variable definitions with proper fallbacks */
:root {
  --font-inter: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-almarai: 'Almarai', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-tajawal: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-changa: 'Changa', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Ensure font classes work even when Next.js font variables aren't available */
.font-inter {
  font-family: var(--font-inter);
}

.font-almarai {
  font-family: var(--font-almarai);
}

.font-tajawal {
  font-family: var(--font-tajawal);
}

.font-changa {
  font-family: var(--font-changa);
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100vw;
  height: 100vh;
  overflow-x: hidden;
}

/* Mobile Safe Area Support */
.mobile-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Mobile Viewport Units */
@supports (height: 100dvh) {
  html, body {
    height: 100dvh; /* Dynamic viewport height for mobile */
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  html, body {
    /* Prevent zoom on input focus */
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }

  /* Better touch targets */
  button, input, select, textarea {
    min-height: 44px; /* Apple's recommended minimum touch target */
  }

  /* Prevent horizontal scroll */
  body {
    overflow-x: hidden;
    position: relative;
  }
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.font-tajawal {
  font-family: 'Tajawal', Arial, sans-serif;
}
.font-changa {
  font-family: 'Changa', Arial, sans-serif;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0.9818 0.0054 95.0986;
    --foreground: 0.3438 0.0269 95.7226;
    --card: 0.9818 0.0054 95.0986;
    --card-foreground: 0.1908 0.0020 106.5859;
    --popover: 1.0000 0 0;
    --popover-foreground: 0.2671 0.0196 98.9390;
    --primary: 0.6171 0.1375 39.0427;
    --primary-foreground: 1.0000 0 0;
    --secondary: 0.9245 0.0138 92.9892;
    --secondary-foreground: 0.4334 0.0177 98.6048;
    --muted: 0.9341 0.0153 90.2390;
    --muted-foreground: 0.6059 0.0075 97.4233;
    --accent: 0.9245 0.0138 92.9892;
    --accent-foreground: 0.2671 0.0196 98.9390;
    --destructive: 0.1908 0.0020 106.5859;
    --destructive-foreground: 1.0000 0 0;
    --border: 0.8847 0.0069 97.3627;
    --input: 0.7621 0.0156 98.3528;
    --ring: 0.5937 0.1673 253.0630;
    --chart-1: 0.5583 0.1276 42.9956;
    --chart-2: 0.6898 0.1581 290.4107;
    --chart-3: 0.8816 0.0276 93.1280;
    --chart-4: 0.8822 0.0403 298.1792;
    --chart-5: 0.5608 0.1348 42.0584;
    --sidebar: 0.9663 0.0080 98.8792;
    --sidebar-foreground: 0.3590 0.0051 106.6524;
    --sidebar-primary: 0.6171 0.1375 39.0427;
    --sidebar-primary-foreground: 0.9881 0 0;
    --sidebar-accent: 0.9245 0.0138 92.9892;
    --sidebar-accent-foreground: 0.3250 0 0;
    --sidebar-border: 0.9401 0 0;
    --sidebar-ring: 0.7731 0 0;
    --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --radius: 0.5rem;
    --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  }

  .dark {
    --background: 0.2679 0.0036 106.6427;
    --foreground: 0.8074 0.0142 93.0137;
    --card: 0.2679 0.0036 106.6427;
    --card-foreground: 0.9818 0.0054 95.0986;
    --popover: 0.3085 0.0035 106.6039;
    --popover-foreground: 0.9211 0.0040 106.4781;
    --primary: 0.6724 0.1308 38.7559;
    --primary-foreground: 1.0000 0 0;
    --secondary: 0.9818 0.0054 95.0986;
    --secondary-foreground: 0.3085 0.0035 106.6039;
    --muted: 0.2213 0.0038 106.7070;
    --muted-foreground: 0.7713 0.0169 99.0657;
    --accent: 0.2130 0.0078 95.4245;
    --accent-foreground: 0.9663 0.0080 98.8792;
    --destructive: 0.6368 0.2078 25.3313;
    --destructive-foreground: 1.0000 0 0;
    --border: 0.3618 0.0101 106.8928;
    --input: 0.4336 0.0113 100.2195;
    --ring: 0.5937 0.1673 253.0630;
    --chart-1: 0.5583 0.1276 42.9956;
    --chart-2: 0.6898 0.1581 290.4107;
    --chart-3: 0.2130 0.0078 95.4245;
    --chart-4: 0.3074 0.0516 289.3230;
    --chart-5: 0.5608 0.1348 42.0584;
    --sidebar: 0.2357 0.0024 67.7077;
    --sidebar-foreground: 0.8074 0.0142 93.0137;
    --sidebar-primary: 0.3250 0 0;
    --sidebar-primary-foreground: 0.9881 0 0;
    --sidebar-accent: 0.1680 0.0020 106.6177;
    --sidebar-accent-foreground: 0.8074 0.0142 93.0137;
    --sidebar-border: 0.9401 0 0;
    --sidebar-ring: 0.7731 0 0;
    --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --radius: 0.5rem;
    --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  }

  @theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Styles for the QR code scanner */
body.scanner-active {
  --background: transparent !important;
  --ion-background-color: transparent !important;
  background-color: transparent !important;
}

.scanner-overlay {
  position: relative;
  z-index: 10;
}

/* Mobile-specific utility classes */
@layer utilities {
  /* Safe area utilities */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  .safe-x {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  .safe-y {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Enhanced safe area classes */
  .safe-area-inset-top {
    padding-top: max(env(safe-area-inset-top), 8px);
  }

  .safe-area-inset-bottom {
    padding-bottom: max(env(safe-area-inset-bottom), 8px);
  }

  .pb-safe {
    padding-bottom: max(env(safe-area-inset-bottom), 16px);
  }

  .pt-safe {
    padding-top: max(env(safe-area-inset-top), 16px);
  }

  /* Mobile navigation spacing */
  .nav-safe-bottom {
    padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
  }

  .floating-safe-bottom {
    bottom: max(env(safe-area-inset-bottom), 8px);
  }

  /* Mobile viewport utilities */
  .h-screen-mobile {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height */
  }

  .min-h-screen-mobile {
    min-height: 100vh;
    min-height: 100dvh;
  }

  /* Touch-friendly utilities */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Enhanced mobile touch interactions */
  .mobile-tap {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    user-select: none;
    touch-action: manipulation;
  }

  /* Better button press feedback */
  .button-press {
    transition: transform 0.1s ease, box-shadow 0.1s ease;
  }

  .button-press:active {
    transform: scale(0.98);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Mobile scroll improvements */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Mobile keyboard handling */
  .keyboard-adjust {
    height: 100vh;
    height: 100dvh;
    transition: height 0.3s ease;
  }

  @media (max-width: 768px) {
    .keyboard-adjust {
      height: calc(100vh - env(keyboard-inset-height, 0px));
      height: calc(100dvh - env(keyboard-inset-height, 0px));
    }

    /* Mobile form keyboard handling */
    .keyboard-open {
      transform: translateY(-10px);
    }

    /* Prevent zoom on input focus */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="tel"],
    textarea,
    select {
      font-size: 16px !important;
      transform-origin: left top;
    }

    /* Better focus styles for mobile */
    input:focus,
    textarea:focus {
      outline: 2px solid hsl(var(--primary));
      outline-offset: 2px;
    }

    /* Mobile-optimized shadows and interactions */
    .mobile-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 12px;
    }

    .mobile-card:active {
      transform: scale(0.98);
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
    }

    /* Enhanced mobile navigation */
    .mobile-nav-tab {
      min-height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      border-radius: 8px;
      transition: all 0.2s ease;
    }

    .mobile-nav-tab.active {
      background: hsl(var(--primary));
      color: hsl(var(--primary-foreground));
      transform: scale(1.02);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  }

  /* PWA and mobile app specific styles */
  @media (display-mode: standalone) {
    body {
      user-select: none;
      -webkit-touch-callout: none;
    }

    /* Hide scrollbars in PWA mode */
    ::-webkit-scrollbar {
      display: none;
    }

    * {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  }

  /* iOS specific optimizations */
  @supports (-webkit-touch-callout: none) {
    .ios-bounce-fix {
      -webkit-overflow-scrolling: touch;
      overflow: scroll;
    }

    .ios-input-fix {
      -webkit-appearance: none;
      border-radius: 0;
    }
  }
}
