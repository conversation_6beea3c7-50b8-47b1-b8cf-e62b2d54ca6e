"use client"

import {
  BanknoteIcon,
  BarChart4Icon,
  ChefHat,
  ClipboardList,
  Home,
  Menu,
  Package,
  Package2,
  Settings,
  Truck,
  User,
  UserCog,
  Users,
  WifiOff,
  Crown,
  Server,
  Database,
  Network,
  Smartphone,
  TestTube,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"

import { useAuth } from "@/lib/context/multi-user-auth-provider"
import { usePermissions } from "@/lib/hooks/use-permissions"
import { useSettings } from '@/lib/context/settings-context'
import { isAdmin as checkIsAdmin } from "@/lib/auth/role-utils"
import { usePathname } from "next/navigation"
import { useMobileLayout, useTouchOptimization } from '@/hooks/use-mobile-layout'
import { useOSDetection } from '@/hooks/use-os-detection'
import { NavMain } from "./nav-main"
import { NavUser } from "./nav-user"
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import React from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { getCurrentRestaurantId as getCurrentRestaurantIdUtil } from '@/lib/db/v4/utils/restaurant-id';
import { NavItem } from './nav-main';
import { RefreshCw } from "lucide-react"; // Import RefreshCw for loading indicator
import { SimpleNetworkIndicator } from './simple-network-indicator';

export function AppSidebar() {
  const currentPathname = usePathname() || ""
  const { user } = useAuth()
  const { hasPageAccess, isOwner, isLoading } = usePermissions()
  const { isCogsEnabled } = useSettings()
  const { isMobile: isSidebarMobile } = useSidebar()
  const { isMobile: isMobileLayout } = useMobileLayout()
  const { isMobile: isMobileOS, platform, isCapacitor } = useOSDetection()
  const { minTouchTarget, recommendedSpacing } = useTouchOptimization()
  
  // Use hybrid approach: OS detection for true mobile platforms, but fallback to screen size for browsers
  // Electron should always be treated as desktop regardless of platform detection
  const { isElectron } = useOSDetection()
  const isMobile = !isElectron && (isCapacitor || platform === 'ios' || platform === 'android' || (platform === 'unknown' && isMobileLayout))
  
  // Debug logging
  console.log('[AppSidebar] Detection:', { isElectron, isCapacitor, platform, isMobileLayout, isMobile })

  // Debug COGS status
  console.log('[AppSidebar] COGS enabled:', isCogsEnabled);

  // Wait for permissions to be ready
  if (isLoading) {
    return (
      <Sidebar 
        variant="sidebar" 
        collapsible={isMobile ? "offcanvas" : "icon"} 
        className="border-r" 
        side="left"
      >
        <SidebarHeader>
          <div className="flex items-center justify-end">
            <SidebarTrigger />
          </div>
        </SidebarHeader>
        <SidebarContent>
          <div className="flex items-center justify-center h-full">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
          </div>
        </SidebarContent>
        <SidebarFooter>
          {/* Optional: Add a basic footer even while loading */}
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    );
  }

  // Use the utility functions directly with the user object
  const isAdmin = checkIsAdmin(user)

  // 👤 User data for NavUser component
  const userData = {
    name: user?.name || 'Unknown User',
    email: user?.email || '',
    avatar: '' // Required by NavUser component
  };

  // 🔗 User menu links
  const userMenuLinks = [
    {
      title: "Profile",
      url: "/profile",
      icon: User,
      mobileOnly: false
    },
    {
      title: "Settings",
      url: "/settings",
      icon: Settings,
      mobileOnly: false
    },
    {
      title: "Mobile View",
      url: "/mobile-ordering",
      icon: Smartphone,
      mobileOnly: true
    }
  ];

  // Define our navigation items structure with direct links
  const createMainNavItems = () => {
    const items = []

    // 📱 Mobile-specific navigation - only show mobile-relevant pages on mobile
    if (isMobile) {
      // Enhanced Mobile Waiter Interface
      if (hasPageAccess('orders')) {
        items.push({
          title: "Waiter Interface",
          url: "/waiter",
          icon: Smartphone,
          isActive: currentPathname === "/waiter",
        })
      }

      // Finance - mobile optimized
      if (hasPageAccess('finance')) {
        items.push({
          title: "Finance",
          url: "/finance",
          icon: BanknoteIcon,
          isActive: currentPathname === "/finance" || currentPathname.startsWith("/finance/"),
        })
      }

      // Analytics - mentioned but not optimized yet
      if (hasPageAccess('analytics')) {
        items.push({
          title: "Analytics",
          url: "/analytics",
          icon: BarChart4Icon,
          isActive: currentPathname === "/analytics" || currentPathname.startsWith("/analytics/"),
        })
      }

      // HTTP Sync Monitor - Mobile specific
      items.push({
        title: "Sync Monitor",
        url: "/p2p-sync",
        icon: Network,
        isActive: currentPathname === "/p2p-sync",
      })

      // Development routes - Mobile Ordering Interface
      if (process.env.NODE_ENV === 'development') {
        items.push({
          title: "Mobile Ordering",
          url: "/mobile-ordering",
          icon: Smartphone,
          isActive: currentPathname === "/mobile-ordering",
        })
      }

      return items
    }

    // 🖥️ Desktop navigation - production menu items
    
    // Menu - Production
    if (hasPageAccess('menu')) {
      items.push({
        title: "Menu",
        url: "/menu",
        icon: Package,
        isActive: currentPathname === "/menu",
      })
    }

    // Orders - Production
    if (hasPageAccess('orders')) {
      items.push({
        title: "Orders",
        url: "/ordering",
        icon: ClipboardList,
        isActive: currentPathname === "/ordering",
      });
    }

    // Inventory - Production
    if (hasPageAccess('inventory')) {
      items.push({
        title: "Inventory",
        url: "/inventory",
        icon: Package2,
        isActive: currentPathname === "/inventory",
      })
    }

    // Suppliers - Production
    if (hasPageAccess('suppliers')) {
      items.push({
        title: "Suppliers",
        url: "/suppliers",
        icon: Truck,
        isActive: currentPathname === "/suppliers",
      })
    }

    // Staff - Production
    if (hasPageAccess('staff')) {
      items.push({
        title: "Staff",
        url: "/staff",
        icon: UserCog,
        isActive: currentPathname === "/staff",
      })
    }

    // Finance - Production
    if (hasPageAccess('finance')) {
      items.push({
        title: "Finance",
        url: "/finance",
        icon: BanknoteIcon,
        isActive: currentPathname === "/finance" || currentPathname.startsWith("/finance/"),
      })
    }

    // Analytics - Production
    if (hasPageAccess('analytics')) {
      items.push({
        title: "Analytics",
        url: "/analytics",
        icon: BarChart4Icon,
        isActive: currentPathname === "/analytics" || currentPathname.startsWith("/analytics/"),
      })
    }

    // Waiter - Production
    if (hasPageAccess('orders')) {
      items.push({
        title: "Waiter",
        url: "/waiter",
        icon: User,
        isActive: currentPathname === "/waiter",
      })
    }

    // Settings - Production
    if (hasPageAccess('settings')) {
      items.push({
        title: "Settings",
        url: "/settings",
        icon: Settings,
        isActive: currentPathname === "/settings",
      })
    }

    // Admin routes - Production
    if (isAdmin) {
      items.push({
        title: "Users",
        url: "/admin/users",
        icon: Users,
        isActive: currentPathname === "/admin/users",
      })
    }

    // Development routes - only in development mode
    if (process.env.NODE_ENV === 'development') {
      // Mobile Ordering Interface
      items.push({
        title: "Mobile Ordering",
        url: "/mobile-ordering",
        icon: Smartphone,
        isActive: currentPathname === "/mobile-ordering",
      })

      // Offline Test
      if (hasPageAccess('settings')) {
        items.push({
          title: "Offline Test",
          url: "/offline-test",
          icon: WifiOff,
          isActive: currentPathname === "/offline-test",
        })
      }


      
      // MongoDB Debug page removed

      // Add HTTP Sync Monitor page
      items.push({
        title: "HTTP Sync Monitor",
        url: "/p2p-sync",
        icon: Network,
        isActive: currentPathname === "/p2p-sync",
      })
    }

    return items
  }

  // Organize items into sections
  const allItems = createMainNavItems();
  
  // 📱 Mobile-specific grouping - simpler, more compact
  if (isMobile) {
    const mobileMainSection = allItems.filter(item => [
      "Waiter Interface", "Finance", "Analytics"
    ].includes(item.title));
    const mobileDevSection = allItems.filter(item => [
      "Mobile Ordering"
    ].includes(item.title));
    const groupedNavItems = [mobileMainSection, mobileDevSection].filter(group => group.length > 0);
    
    // Log mobile navigation
    console.log('[AppSidebar] Mobile groupedNavItems:', groupedNavItems);
    
    return (
       <Sidebar
         variant="sidebar"
         collapsible="offcanvas"
         className="border-r"
         side="left"
       >
         <SidebarHeader>
           <div className="flex items-center justify-end">
             <SidebarTrigger />
           </div>
         </SidebarHeader>
         <SidebarContent>
           <NavMain groupedItems={groupedNavItems} />
         </SidebarContent>
         <SidebarFooter>
           <SimpleNetworkIndicator className="mb-2" />
           <NavUser user={userData} extraLinks={userMenuLinks.filter(link => !link.mobileOnly || isMobile)} />
         </SidebarFooter>
         <SidebarRail />
       </Sidebar>
     )
  }
  
  // 🖥️ Desktop grouping - production sections
  const frontOfHouse = allItems.filter(item => [
    "Menu", "Orders", "Waiter"
  ].includes(item.title));
  const backOfHouse = allItems.filter(item => [
    "Inventory", "Suppliers", "Staff"
  ].includes(item.title));
  const dataSection = allItems.filter(item => [
    "Finance", "Analytics"
  ].includes(item.title));
  
  // Development sections - only in development mode
  const devSection = allItems.filter(item => [
    "Mobile Ordering", "Offline Test", "Printer Test"
  ].includes(item.title));
  const syncSection = allItems.filter(item => [
    "HTTP Sync Monitor"
  ].includes(item.title));
  const adminSection = allItems.filter(item => [
    "Settings", "Users"
  ].includes(item.title));
  
  // Group items based on environment
  const groupedNavItems = process.env.NODE_ENV === 'development' 
    ? [frontOfHouse, backOfHouse, dataSection, devSection, syncSection, adminSection].filter(group => group.length > 0)
    : [frontOfHouse, backOfHouse, dataSection, adminSection].filter(group => group.length > 0);

  // Log the groupedNavItems to inspect its contents
  console.log('[AppSidebar] groupedNavItems:', groupedNavItems);

  return (
    <Sidebar
      variant="sidebar"
      collapsible={isMobile ? "offcanvas" : "icon"}
      className="border-r"
      side="left"
    >
      <SidebarHeader>
        <div className="flex items-center justify-end">
          <SidebarTrigger />
        </div>
      </SidebarHeader>
      <SidebarContent>
        <NavMain groupedItems={groupedNavItems} />
      </SidebarContent>
      <SidebarFooter>
        <SimpleNetworkIndicator className="mb-2" />
        <NavUser user={userData} extraLinks={userMenuLinks.filter(link => !link.mobileOnly || isMobile)} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}