import { contextBridge, ip<PERSON><PERSON><PERSON><PERSON>, IpcRendererEvent } from 'electron';

// Add PouchDB namespace for type definitions
declare namespace PouchDB {
  namespace Core {
    interface GetOptions {
      rev?: string;
      revs?: boolean;
      revs_info?: boolean;
      open_revs?: string[] | 'all';
      conflicts?: boolean;
      attachments?: boolean;
      [propName: string]: any;
    }
    
    interface PutDocument<Content extends {}> {
      _id?: string;
      _rev?: string;
      _deleted?: boolean;
      _attachments?: any;
      [propName: string]: any;
    }
    
    interface BulkDocsOptions {
      new_edits?: boolean;
    }
  }
  
  namespace Find {
    interface CreateIndexOptions {
      index: {
        fields: string[];
        name?: string;
        ddoc?: string;
        type?: string;
      };
    }
    
    interface FindRequest<T> {
      selector: any;
      fields?: string[];
      sort?: any[];
      limit?: number;
      skip?: number;
      use_index?: string | [string, string];
    }
  }
}

// Set the global flag for the renderer to detect Electron environment
// This is critical for proper database initialization
contextBridge.exposeInMainWorld('IS_DESKTOP_APP', true);

// Remove old activeDbPaths and beforeunload related to old DB management
// const activeDbPaths = new Set<string>();

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Send a message from renderer to main
  send: (channel: string, data: any) => {
    const validChannels = ['toMain', 'app-ready', 'trigger-action'];
    if (validChannels.includes(channel)) {
      ipcRenderer.send(channel, data);
    }
  },
  // Receive a message from main to renderer
  receive: (channel: string, func: (...args: any[]) => void) => {
    const validChannels = [
      'fromMain', 
      'app-event', 
      'system-update', 
      'db-event',
      // NEW: P2P sync events
      'peer-discovered',
      'peer-lost',
      'sync-status-updated',
      // NEW: Auto-updater events
      'update-available',
      'download-progress',
      'update-downloaded',
      'update-error'
    ];
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, (_event: IpcRendererEvent, ...args: any[]) => func(...args));
    }
  },
  // Invoke a method and get a promise response
  invoke: (channel: string, ...args: any[]) => {
    // This generic invoke is less used now that specific methods are defined below
    // but can be kept for other non-DB related IPC calls.
    const validChannels = [
      'get-app-info', 
      'perform-action', 
      'get-system-info',
      // PouchDB specific channels
      'ensure-db-opened',
      'pouchdb-get',
      'pouchdb-put',
      'pouchdb-remove',
      'pouchdb-bulk-docs',
      'pouchdb-create-index',
      'pouchdb-find',
      'pouchdb-close',
      'pouchdb-destroy',
      // NEW: P2P sync channels
      'p2p-get-peers',
      'p2p-get-sync-status',
      'p2p-get-mdns-status',
      'p2p-get-system-id',
      'p2p-get-server-port',
      'p2p-start-sync',
      'p2p-stop-sync',
      'p2p-stop-all-syncs-with-peer',
      // NEW: Database listing channel
      'get-database-list',
      // CouchDB channels
      'get-couchdb-url',
      'get-couchdb-databases',
      // NEW: Local CouchDB sync channels
      'start-local-sync',
      'stop-local-sync',
      'get-local-sync-status',
      // NEW: Printer and USB device discovery
      'get-usb-devices',
      'get-system-printers',
      // NEW: Auto-updater actions
      'quit-and-install',
      // NEW: CouchDB port information
      'get-couchdb-port',
      // NEW: Missing P2P debug and configuration channels
      'debug-bonjour-module',
      'p2p-configure-internet-sync',
      'p2p-get-internet-sync-status',
      'p2p-restart-mdns'
    ];
    if (validChannels.includes(channel)) {
      return ipcRenderer.invoke(channel, ...args);
    }
    return Promise.reject(new Error(`Unauthorized or unknown channel: ${channel}`));
  },

  // Get the current CouchDB port from main process
  getCouchDBPort: async (): Promise<number> => {
    return ipcRenderer.invoke('get-couchdb-port');
  },
  
  // --- NEW: Generic offline-first data access methods ---
  // These map to the corresponding methods in our offline-first data fetcher
  getPouchData: async (dbName: string, options: any = {}): Promise<any> => {
    console.log(`[preload.ts] getPouchData for ${dbName}`, options);
    try {
      if (options.id) {
        // Single document fetch
        return ipcRenderer.invoke('pouchdb-get', dbName, options.id);
      } else {
        // Collection fetch using find
        return ipcRenderer.invoke('pouchdb-find', dbName, {
          selector: options.selector || {},
          sort: options.sort,
          limit: options.limit,
          skip: options.skip,
          fields: options.fields
        });
      }
    } catch (error) {
      console.error(`[preload.ts] Error in getPouchData for ${dbName}:`, error);
      throw error;
    }
  },
  
  savePouchData: async (dbName: string, data: any): Promise<any> => {
    console.log(`[preload.ts] savePouchData for ${dbName}`, data);
    try {
      // Ensure the database exists
      await ipcRenderer.invoke('ensure-db-opened', dbName);
      
      // Handle arrays as bulk operations
      if (Array.isArray(data)) {
        return ipcRenderer.invoke('pouchdb-bulk-docs', dbName, data);
      }
      
      // Handle single document
      return ipcRenderer.invoke('pouchdb-put', dbName, data);
    } catch (error) {
      console.error(`[preload.ts] Error in savePouchData for ${dbName}:`, error);
      throw error;
    }
  },
  
  findPouchData: async (dbName: string, query: any = {}): Promise<any> => {
    console.log(`[preload.ts] findPouchData for ${dbName}`, query);
    try {
      // Ensure the database exists
      await ipcRenderer.invoke('ensure-db-opened', dbName);
      
      // Convert query params to PouchDB find query
      const selector: Record<string, any> = {};
      
      // Process query parameters into a selector
      Object.entries(query).forEach(([key, value]) => {
        if (value === undefined || value === null) return;
        
        // Handle special query parameters
        if (key === 'sort' || key === 'limit' || key === 'skip' || key === 'fields') {
          return; // These are handled separately
        }
        
        // Convert string "true"/"false" to boolean
        if (value === 'true') selector[key] = true;
        else if (value === 'false') selector[key] = false;
        else selector[key] = value;
      });
      
      // Build the find request
      const findRequest: PouchDB.Find.FindRequest<any> = {
        selector: Object.keys(selector).length > 0 ? selector : { _id: { $gt: null } },
        sort: query.sort ? JSON.parse(query.sort) : undefined,
        limit: query.limit ? parseInt(query.limit) : undefined,
        skip: query.skip ? parseInt(query.skip) : undefined,
        fields: query.fields ? query.fields.split(',') : undefined
      };
      
      return ipcRenderer.invoke('pouchdb-find', dbName, findRequest);
    } catch (error) {
      console.error(`[preload.ts] Error in findPouchData for ${dbName}:`, error);
      throw error;
    }
  },
  
  // --- PouchDB Operations via IPC --- 
  database: {
    ensureDbOpened: async (dbIdentifier: string): Promise<any> => {
      console.log(`[preload.ts] Invoking 'ensure-db-opened' for database: ${dbIdentifier}`);
      try {
        const result = await ipcRenderer.invoke('ensure-db-opened', dbIdentifier);
        console.log(`[preload.ts] Result from 'ensure-db-opened': `, result);
        return result;
      } catch (error) {
        console.error(`[preload.ts] Error in 'ensure-db-opened' for ${dbIdentifier}:`, error);
        throw error;
      }
    },
    get: async (dbIdentifier: string, docId: string, options?: PouchDB.Core.GetOptions): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-get', dbIdentifier, docId, options);
    },
    put: async (dbIdentifier: string, doc: PouchDB.Core.PutDocument<any>): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-put', dbIdentifier, doc);
    },
    remove: async (dbIdentifier: string, docId: string, rev: string): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-remove', dbIdentifier, docId, rev);
    },
    bulkDocs: async (dbIdentifier: string, docsParam: Array<any> | { docs: Array<any> }, options?: PouchDB.Core.BulkDocsOptions): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-bulk-docs', dbIdentifier, docsParam, options);
    },
    createIndex: async (dbIdentifier: string, indexDefinition: PouchDB.Find.CreateIndexOptions): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-create-index', dbIdentifier, indexDefinition);
    },
    find: async (dbIdentifier: string, findRequest: PouchDB.Find.FindRequest<any>): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-find', dbIdentifier, findRequest);
    },
    close: async (dbIdentifier: string): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-close', dbIdentifier);
    },
    destroy: async (dbIdentifier: string): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-destroy', dbIdentifier);
    },
    // NEW: Local sync with CouchDB methods
    startLocalSync: async (dbIdentifier: string): Promise<any> => {
      return ipcRenderer.invoke('start-local-sync', dbIdentifier);
    },
    stopLocalSync: async (dbIdentifier: string): Promise<any> => {
      return ipcRenderer.invoke('stop-local-sync', dbIdentifier);
    },
    getLocalSyncStatus: async (): Promise<any> => {
      return ipcRenderer.invoke('get-local-sync-status');
    },
    // Clear cached database instances (for account switching)
    clearCache: async (restaurantIdPattern?: string): Promise<any> => {
      console.log(`[preload.ts] Invoking 'database:clear-cache'${restaurantIdPattern ? ` for pattern: ${restaurantIdPattern}` : ''}`);
      return ipcRenderer.invoke('database:clear-cache', restaurantIdPattern);
    }
  },
  
  // --- P2P Sync Operations via IPC --- 
  p2pSync: {
    // Get all discovered peers on the network
    getPeers: async (): Promise<any> => {
      return ipcRenderer.invoke('p2p-get-peers');
    },
    
    // Get the status of all active syncs
    getSyncStatus: async (): Promise<any> => {
      return ipcRenderer.invoke('p2p-get-sync-status');
    },
    
    // Get the current mDNS status
    getMdnsStatus: async (): Promise<'not_running' | 'running' | 'error'> => {
      try {
        const result = await ipcRenderer.invoke('p2p-get-mdns-status');
        console.log('[preload.ts] Got mDNS status:', result);
        return result;
      } catch (error) {
        console.error('[preload.ts] Error getting mDNS status:', error);
        return 'error';
      }
    },
    
    // Get system ID for diagnostics
    getSystemId: async (): Promise<string> => {
      return ipcRenderer.invoke('p2p-get-system-id');
    },
    
    // Get server port for diagnostics
    getServerPort: async (): Promise<number> => {
      return ipcRenderer.invoke('p2p-get-server-port');
    },
    
    // Get local service info for diagnostics
    getServiceInfo: async (): Promise<any> => {
      return ipcRenderer.invoke('p2p-get-service-info');
    },
    
    // Get mDNS info
    getMdnsInfo: async (): Promise<any> => {
      try {
        const result = await ipcRenderer.invoke('p2p-get-mdns-info');
        console.log('[preload.ts] Got mDNS info:', result);
        return result;
      } catch (error) {
        console.error('[preload.ts] Error getting mDNS info:', error);
        return { published: false, error: String(error) };
      }
    },
    
    // Get detailed mDNS diagnostics
    getMdnsDiagnostics: async (): Promise<any> => {
      try {
        const result = await ipcRenderer.invoke('p2p-get-mdns-diagnostics');
        console.log('[preload.ts] Got mDNS diagnostics:', result);
        return result;
      } catch (error) {
        console.error('[preload.ts] Error getting mDNS diagnostics:', error);
        return { error: String(error) };
      }
    },
    
    // Restart the mDNS service
    restartMdns: async (): Promise<any> => {
      try {
        console.log('[preload.ts] Attempting to restart mDNS service');
        const result = await ipcRenderer.invoke('p2p-restart-mdns');
        console.log('[preload.ts] mDNS restart result:', result);
        return result;
      } catch (error) {
        console.error('[preload.ts] Error restarting mDNS service:', error);
        return { success: false, error: String(error) };
      }
    },
    
    // Reset the mDNS system to recover from errors
    resetMdns: async (): Promise<any> => {
      // mDNS/Bonjour logic removed - stub for future implementation
      return { success: false, error: 'mDNS/Bonjour logic removed' };
    },
    
    // Start syncing with a peer
    startSync: async (peerId: string, dbName: string, direction: 'push' | 'pull' | 'both' = 'both'): Promise<any> => {
      return ipcRenderer.invoke('p2p-start-sync', peerId, dbName, direction);
    },
    
    // Stop syncing with a peer for a specific database
    stopSync: async (peerId: string, dbName: string): Promise<any> => {
      return ipcRenderer.invoke('p2p-stop-sync', peerId, dbName);
    },
    
    // Stop all syncs with a peer
    stopAllSyncsWithPeer: async (peerId: string): Promise<any> => {
      return ipcRenderer.invoke('p2p-stop-all-syncs-with-peer', peerId);
    },
    
    // Listen for peer discovery events
    onPeerDiscovered: (callback: (peerInfo: any) => void) => {
      ipcRenderer.on('peer-discovered', (_event: IpcRendererEvent, peerInfo: any) => callback(peerInfo));
    },
    
    // Listen for peer lost events
    onPeerLost: (callback: (peerId: string) => void) => {
      ipcRenderer.on('peer-lost', (_event: IpcRendererEvent, peerId: string) => callback(peerId));
    },
    
    // Listen for sync status updates
    onSyncStatusUpdated: (callback: (status: any) => void) => {
      ipcRenderer.on('sync-status-updated', (_event: IpcRendererEvent, status: any) => callback(status));
    }
  },

  // mDNS discovery functions
  mdnsDiscovery: {
    startDiscovery: (serviceType: string) => ipcRenderer.invoke('mdns:start-discovery', serviceType),
    stopDiscovery: () => ipcRenderer.invoke('mdns:stop-discovery'),
    getDiscoveredServices: () => ipcRenderer.invoke('mdns:get-services'),
    onServiceUpdated: (callback: (services: any[]) => void) => {
      const subscription = (_event: IpcRendererEvent, services: any[]) => callback(services);
      ipcRenderer.on('mdns:service-updated', subscription);
      
      // Return a cleanup function to remove the listener
      return () => {
        ipcRenderer.removeListener('mdns:service-updated', subscription);
      };
    }
  },

  // --- CouchDB Operations via IPC ---
  couchdb: {
    // Get the URL of the running CouchDB server
    getUrl: async (): Promise<any> => {
      return ipcRenderer.invoke('get-couchdb-url');
    },
    // Get list of CouchDB databases
    getDatabases: async (): Promise<any> => {
      return ipcRenderer.invoke('get-couchdb-databases');
    }
  },

  // --- Auto-updater Operations ---
  // Listen for update available events
  onUpdateAvailable: (callback: (info: any) => void) => {
    ipcRenderer.on('update-available', (_event: IpcRendererEvent, info: any) => callback(info));
  },
  
  // Listen for download progress events
  onDownloadProgress: (callback: (progress: any) => void) => {
    ipcRenderer.on('download-progress', (_event: IpcRendererEvent, progress: any) => callback(progress));
  },
  
  // Listen for update downloaded events
  onUpdateDownloaded: (callback: (info: any) => void) => {
    ipcRenderer.on('update-downloaded', (_event: IpcRendererEvent, info: any) => callback(info));
  },
  
  // Listen for update error events
  onUpdateError: (callback: (error: any) => void) => {
    ipcRenderer.on('update-error', (_event: IpcRendererEvent, error: any) => callback(error));
  },
  
  // Quit and install update
  quitAndInstall: async (): Promise<void> => {
    return ipcRenderer.invoke('quit-and-install');
  },


});

// Forward P2P logs from main process to renderer
contextBridge.exposeInMainWorld('p2pLog', {
  onLog: (callback: (msg: string) => void) => {
    ipcRenderer.on('p2p-log', (_event: IpcRendererEvent, msg: string) => callback(msg));
  }
});

// Remove old beforeunload handler
// window.addEventListener('beforeunload', async () => { ... }); 

// P2P Sync API - Register event listener for p2p-log events
if (ipcRenderer.listenerCount('p2p-log') === 0) {
  ipcRenderer.on('p2p-log', (_, message) => {
    // Forward log messages to any listeners in the renderer
    document.dispatchEvent(new CustomEvent('p2p-log', { detail: message }));
  });
}