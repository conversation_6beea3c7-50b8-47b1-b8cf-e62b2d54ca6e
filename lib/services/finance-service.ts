'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  getAllCashTransactions,
  createCashTransaction,
  getAllOrders
} from '@/lib/db/v4';

// Type definitions for the finance service
export interface FinancialTransaction {
  id: string;
  type: 'sales' | 'manual_in' | 'manual_out';
  amount: number;
  relatedDocId?: string;
  description: string;
  time: string;
  performedBy: string;
  metadata?: any;
}

// knowledge:start v4 direct implementation
// knowledge: v4 cash register logic (with UI compatibility stubs and safe signatures)
export function useCashRegister() {
  const [transactions, setTransactions] = useState<FinancialTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, user } = useAuth();

  const refreshData = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setTransactions([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      // Check if database is properly initialized
      const { databaseV4 } = await import('@/lib/db/v4/core/db-instance');
      
      if (!databaseV4.isInitialized || !databaseV4.getCurrentRestaurantId()) {
        console.warn('[FinanceService] Database not properly initialized, waiting...');
        
        try {
          const restaurantId = user.restaurantId;
          if (!restaurantId) {
            throw new Error('No restaurant ID available');
          }
          
          await databaseV4.waitForInitialization(restaurantId, 30000);
          console.log('[FinanceService] Database initialization completed');
        } catch (waitError) {
          console.error('[FinanceService] Database initialization failed:', waitError);
          setError('Database not ready - please refresh the page');
          setTransactions([]);
          return;
        }
      }

      const allTransactions = await getAllCashTransactions();
      console.log('🔍 Finance Service: Raw transactions from database:', allTransactions);
      
      setTransactions(
        Array.isArray(allTransactions)
          ? allTransactions.map((tx: any, i: number) => ({
              id: tx?._id || `tx_${i}_${Date.now()}`,
              type: tx?.transactionType || tx?.type || 'manual_in',
              amount: typeof tx?.amount === 'number' ? tx.amount : 0,
              relatedDocId: tx?.relatedDocId || '',
              description: tx?.description || '',
              time: tx?.time || new Date().toISOString(),
              performedBy: tx?.performedBy || 'Unknown',
              metadata: tx?.metadata
            }))
          : []
      );
      
      console.log('🔍 Finance Service: Processed transactions:', transactions.length);
    } catch (err) {
      console.error('[FinanceService] Error loading cash transactions:', err);
      setError('Failed to load cash transactions');
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, user]);

  useEffect(() => {
      refreshData();
  }, [refreshData]);

  // Accepts (type, amount, description, performedBy?) for UI compatibility
  const addCashTransaction = async (
    type: 'manual_in' | 'manual_out',
    amount: number,
    description: string,
    performedBy?: string // optional, for UI compatibility
  ) => {
    if (!isAuthenticated || !user) {
      setError('Not authenticated or user not available');
      return false;
    }
    setLoading(true);
    try {
      // Only allow negative amounts for manual_out
      if (type === 'manual_out') {
        await createCashTransaction({
          type,
          amount: -Math.abs(amount),
          description,
          time: new Date().toISOString(),
          performedBy: performedBy || user.name || user.id || 'Unknown User',
        });
      } else {
        await createCashTransaction({
          type,
          amount: Math.abs(amount),
          description,
          time: new Date().toISOString(),
          performedBy: performedBy || user.name || user.id || 'Unknown User',
        });
      }
      await refreshData();
      return true;
    } catch (err) {
      setError('Failed to add transaction');
      return false;
    } finally {
      setLoading(false);
    }
  };



  // Calculate the current cash balance (caisse)
  const calculateCashBalance = useCallback(() => {
    return transactions.reduce((total, tx) => total + (typeof tx.amount === 'number' ? tx.amount : 0), 0);
  }, [transactions]);

  const calculateSessionTotals = () => {
    if (!transactions.length) return { totalIn: 0, totalOut: 0 };
    const totalIn = transactions.filter(tx => tx.amount > 0).reduce((sum, tx) => sum + tx.amount, 0);
    const totalOut = transactions.filter(tx => tx.amount < 0).reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    return { totalIn, totalOut };
  };

  return {
    transactions: Array.isArray(transactions) ? transactions : [],
    loading,
    error,
    addCashTransaction,
    refreshData,
    calculateSessionTotals,
    cashBalance: calculateCashBalance()
  };
}



// knowledge: v4 register order payment and add to caisse
export async function registerOrderPayment(
  orderId: string,
  amount: number, // This should be the total value of goods sold
  receivedAmount?: number, // This is the amount the customer paid
  cashier?: string
): Promise<boolean> {
  // Add a cash transaction of type 'sales' for the order
  try {
    // For 'sales' transactions, the amount should always be the actual value of the goods sold.
    // The 'receivedAmount' is for calculating change, not for recording sales value in the caisse.
    const salesAmount = amount; // Use the 'amount' parameter which represents the order total.

    // Use cashier if provided, otherwise 'System'
    const performedBy = cashier || 'System';
    await createCashTransaction({
      type: 'sales',
      amount: salesAmount,
      description: `Order payment for order ${orderId}`,
      time: new Date().toISOString(),
      performedBy,
      relatedDocId: orderId
    });
    return true;
  } catch (err) {
    // Optionally log error
    return false;
  }
}
// knowledge: v4 register order payment and add to caisse end

// knowledge: REMOVE useFinancialSummary (no summary, only real-time cash)
// (function removed)
// knowledge: REMOVE useFinancialSummary end
// knowledge: v4 migration end

// knowledge: v4 paid order income calculation start
/**
 * Get the total paid income from orders (v4 only)
 * Optionally accepts a date range (startDate, endDate in ISO format)
 */
export async function getTotalPaidIncomeV4(startDate?: string, endDate?: string): Promise<number> {
  // Fetch all orders
  const allOrders = await getAllOrders();
  // Filter for paid or partially paid orders
  const paidOrders = allOrders.filter(order =>
    (order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid') &&
    (!startDate || new Date(order.createdAt) >= new Date(startDate)) &&
    (!endDate || new Date(order.createdAt) <= new Date(endDate))
  );
  // Sum the paid amounts (prefer paymentDetails.amountPaid, fallback to order.total)
  const totalIncome = paidOrders.reduce((sum, order) => {
    const paid = order.paymentDetails?.amountPaid ?? order.total ?? 0;
    return sum + paid;
  }, 0);
  return totalIncome;
}
// knowledge: v4 paid order income calculation end

// knowledge: v4 expenses hook start
export function useExpenses() {
  const { isAuthenticated, user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const addExpense = async (
    amount: number,
    description: string,
    category?: string
  ) => {
    if (!isAuthenticated || !user) {
      setError('Not authenticated');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      await createCashTransaction({
        type: 'manual_out',
        amount: -Math.abs(amount),
        description: `${category ? `[${category}] ` : ''}${description}`,
        time: new Date().toISOString(),
        performedBy: user.name || user.id || 'Unknown User',
      });
      
      return true;
    } catch (err) {
      console.error('[useExpenses] Error adding expense:', err);
      setError('Failed to add expense');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    addExpense,
    loading,
    error
  };
}
// knowledge: v4 expenses hook end

// knowledge:end v4 direct implementation