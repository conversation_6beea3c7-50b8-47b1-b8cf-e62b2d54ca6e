'use client';

import { useState, useEffect, useCallback } from 'react';
import { getCurrentRestaurantId } from '@/lib/db/v4/utils/restaurant-id';
import { 
  OrderDocument, 
  OrderItem, 
  Customer, 
  DeliveryPerson,
  Order 
} from '@/lib/db/v4/schemas/order-schema';
import { getAllOrders, createOrder as v4CreateOrder, updateOrder as v4UpdateOrder, getOrder as v4GetOrder, deleteOrder as v4DeleteOrder } from '@/lib/db/v4/operations/order-ops';
import { createOrGetFreelancer } from '@/lib/db/v4/operations/freelancer-ops';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';

// Type for new order creation
type NewOrder = Omit<OrderDocument, '_id' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt'>;

// Helper function to convert OrderDocument to Order
const toOrder = (doc: OrderDocument): Order => {
  return { ...doc, id: doc._id };
};

// Helper function to convert OrderDocument[] to Order[]
const toOrders = (docs: OrderDocument[]): Order[] => {
  return docs.map(toOrder);
};

interface UseOrderReturn {
  orders: Order[];
  activeOrders: Order[];
  isLoading: boolean;
  error: Error | null;
  syncState: {
    status: string;
    error: Error | null;
    lastSync: Date | null;
    pendingChanges: number;
  };
  isReady: boolean;
  refreshOrders: (options?: { forceRefresh?: boolean }) => Promise<void>;
  createOrder: (order: NewOrder) => Promise<Order>;
  updateOrder: (orderId: string, updates: Partial<OrderDocument>) => Promise<Order>;
  getOrder: (orderId: string) => Promise<Order | null>;
  deleteOrder: (orderId: string) => Promise<void>;
}

/**
 * useOrderV4 hook - provides order functionality using the v4 database
 */
export function useOrderV4(): UseOrderReturn {
  const { isAuthenticated, user } = useAuth();
  const { 
    isDbInitialized, 
    isLoadingDb, 
    dbInitializeError,
    currentDbRestaurantId 
  } = useUnifiedDB();

  const [orders, setOrders] = useState<Order[]>([]);
  const [activeOrders, setActiveOrders] = useState<Order[]>([]);
  const [isOrdersLoading, setIsOrdersLoading] = useState(true);
  const [ordersError, setOrdersError] = useState<Error | null>(null);
  const [isHookReady, setIsHookReady] = useState(false);

  const [syncState, setSyncState] = useState<{
    status: string;
    error: Error | null;
    lastSync: Date | null;
    pendingChanges: number;
  }>({
    status: 'disconnected',
    error: null,
    lastSync: null,
    pendingChanges: 0
  });

  // This effect now SOLELY manages the isHookReady state based on dependencies.
  useEffect(() => {
    console.log('[useOrderV4] Checking readiness conditions:', {
      isAuthenticated,
      isDbInitialized,
      isLoadingDb,
      dbInitializeError: !!dbInitializeError,
      currentDbRestaurantId
    });
    
    if (isAuthenticated && isDbInitialized && !isLoadingDb && !dbInitializeError) {
      console.log('[useOrderV4] Conditions met: Authenticated and DB Initialized. Setting isHookReady to true.');
      setIsHookReady(true);
      setOrdersError(null); // Clear any previous DB init error
    } else {
      console.log('[useOrderV4] Conditions NOT met for hook readiness. Setting isHookReady to false.');
      setIsHookReady(false);
      if (dbInitializeError) {
        setOrdersError(new Error(`Database initialization failed: ${dbInitializeError.message}`));
      }
      // If we are not ready (e.g. DB loading, not auth, etc.), ensure loading indicators are active.
      if (isLoadingDb || (!isDbInitialized && !dbInitializeError && isAuthenticated)) {
         // Only set loading true if we expect to load eventually (e.g. authenticated but DB not ready yet)
        setIsOrdersLoading(true);
      }
    }
  }, [isAuthenticated, isDbInitialized, isLoadingDb, dbInitializeError]);

  // Renamed the original second useEffect to clarify its purpose
  // This effect handles the INITIAL data load once the hook becomes ready.
  // It also handles cleanup if the hook becomes not_ready.
  useEffect(() => {
    if (isHookReady) {
      const initialLoad = async () => {
        console.log('[useOrderV4] Hook is now ready. Performing initial order load.');
        setIsOrdersLoading(true);
        try {
          await refreshOrders({ forceRefresh: true });
          // setOrdersError(null); // refreshOrders now handles its own errors without clearing data
        } catch (err) {
          // This catch is mostly for unexpected errors from refreshOrders itself, though it has its own try/catch.
          // refreshOrders will setOrdersError internally.
          console.error('[useOrderV4] Error during initialLoad wrapper: ', err);
          // setOrdersError(err instanceof Error ? err : new Error(String(err))); // already set by refreshOrders
        } finally {
          setIsOrdersLoading(false);
        }
      };
      initialLoad();
    } else {
      // If hook becomes not ready (e.g., user logs out, DB error after init)
      console.log('[useOrderV4] Hook is NOT ready. Clearing orders and setting loading state.');
      setOrders([]);
      setActiveOrders([]);
      // setIsOrdersLoading(true); // No, this would fight with the readiness effect.
                               // If not ready, the previous effect should manage loading state.
    }
  }, [isHookReady]); // Dependency: Only isHookReady

  // Periodic refresh for orders (every 30s)
  // This useEffect remains largely the same.
  // The isAuthenticated check here is a good guard, though isHookReady should also cover it.
  useEffect(() => {
    if (isHookReady && isAuthenticated) {
      const intervalId = setInterval(() => {
        console.log('[OrderV4] Periodic refresh...');
        refreshOrders({ forceRefresh: false });
      }, 30000); // 30 seconds
      return () => clearInterval(intervalId);
    }
  }, [isHookReady, isAuthenticated]);

  // Refresh orders
  const refreshOrders = useCallback(async ({ forceRefresh = false } = {}) => {
    if (!isHookReady) {
      // This check is now primarily for the periodic calls,
      // as the initial load is gated by the new useEffect.
      console.log('[OrderV4.refreshOrders] Skipped: Hook not ready (checked inside refreshOrders).');
      return;
    }
    if (!forceRefresh && isOrdersLoading) {
      return;
    }
    setIsOrdersLoading(true);
    try {
      // Get all orders using the robust fallback method that tries multiple approaches
      const restaurantId = getCurrentRestaurantId();
      console.log(`[OrderV4] Fetching orders for DB: restaurant-${restaurantId}`);
      const allOrders = await getAllOrders();
      console.log(`[OrderV4] Found ${allOrders.length} orders using robust query method`);
      
      if (allOrders.length === 0) {
        console.warn('[OrderV4] No orders found in the database after trying all methods.');
      }
      setOrders(toOrders(allOrders));
      // Type-safe filter for active orders
      const active = allOrders.filter((order: OrderDocument) =>
        order.status === 'pending' || order.status === 'preparing' || order.status === 'served'
      );
      setActiveOrders(toOrders(active));
      setOrdersError(null);
      // No global event dispatch here to avoid refresh loops
    } catch (err) {
      console.error('[OrderV4] Error loading orders:', err);
      setOrdersError(err instanceof Error ? err : new Error('Failed to load orders'));
    } finally {
      setIsOrdersLoading(false);
      console.log('[OrderV4] Order refresh completed');
    }
  }, [isHookReady, isOrdersLoading]);

  // 🆕 GLOBAL EVENT LISTENERS — keep multiple hook instances in sync
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleExternalOrderChange = () => {
      // Force refresh to pull the latest data created elsewhere (other components/tabs)
      refreshOrders({ forceRefresh: true });
    };

    window.addEventListener('order-created', handleExternalOrderChange);
    
    return () => {
      window.removeEventListener('order-created', handleExternalOrderChange);
    };
  }, [refreshOrders]);

  // 🆕 SYNC EVENT LISTENERS — detect orders from sync and trigger auto-print
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleDatabaseChange = (event: any) => {
      const change = event.detail;
      
      // Check if this is a new order from sync (not local creation)
      if (change?.doc?.type === 'order_document' && 
          change?.doc?.status === 'pending' &&
          !change?.isLocal) {
        
        console.log('[OrderV4] Synced order detected:', change.doc._id);
        
        // Dispatch order-synced event for auto-print service
        window.dispatchEvent(new CustomEvent('order-synced', {
          detail: { 
            order: toOrder(change.doc), 
            source: 'sync' 
          }
        }));
      }
    };

    // Listen for PouchDB changes that indicate synced orders
    window.addEventListener('pouchdb-change', handleDatabaseChange);
    
    return () => {
      window.removeEventListener('pouchdb-change', handleDatabaseChange);
    };
  }, []);

  // Create an order
  const createOrder = useCallback(async (order: NewOrder): Promise<Order> => {
    console.log('[OrderV4.createOrder] 🎯 Starting order creation process...');
    console.log('[OrderV4.createOrder] Hook ready state:', { isHookReady, isOrdersLoading });
    console.log('[OrderV4.createOrder] Input order data:', {
      customerName: order.customer?.name,
      itemCount: order.items?.length,
      total: order.total,
      orderType: order.orderType,
      status: order.status
    });
    
    if (!isHookReady) {
      const error = new Error('Order hook not ready (DB not initialized or user not authenticated)');
      console.error('[OrderV4.createOrder] ❌ Hook not ready:', error);
      setOrdersError(error);
      throw error;
    }

    try {
      console.log('[OrderV4.createOrder] 📞 Calling v4CreateOrder...');
      const createdOrder = await v4CreateOrder(order);
      console.log('[OrderV4.createOrder] ✅ Order created successfully:', {
        id: createdOrder._id,
        status: createdOrder.status,
        total: createdOrder.total,
        hasRev: !!createdOrder._rev
      });

      // Convert to local format
      const localOrder = toOrder(createdOrder);
      console.log('[OrderV4.createOrder] 🔄 Converted to local format:', {
        id: localOrder.id,
        status: localOrder.status,
        total: localOrder.total
      });

      // Update local state immediately for better UX
      console.log('[OrderV4.createOrder] 📝 Updating local state...');
      setOrders(prevOrders => {
        const newOrders = [localOrder, ...prevOrders];
        console.log('[OrderV4.createOrder] Local state updated:', {
          previousCount: prevOrders.length,
          newCount: newOrders.length,
          newOrderId: localOrder.id
        });
        return newOrders;
      });
      
      // Update active orders if applicable
      if (localOrder.status === 'pending' || localOrder.status === 'preparing' || localOrder.status === 'served') {
        console.log('[OrderV4.createOrder] 📝 Adding to active orders...');
        setActiveOrders(prevActive => {
          const newActive = [localOrder, ...prevActive];
          console.log('[OrderV4.createOrder] Active orders updated:', {
            previousCount: prevActive.length,
            newCount: newActive.length
          });
          return newActive;
        });
      }

      // Refresh orders from database to ensure consistency
      console.log('[OrderV4.createOrder] 🔄 Refreshing orders from database...');
      try {
        await refreshOrders({ forceRefresh: true });
        console.log('[OrderV4.createOrder] ✅ Orders refreshed successfully');
      } catch (refreshError) {
        console.error('[OrderV4.createOrder] ⚠️ Failed to refresh orders after creation:', refreshError);
        // Don't fail the creation if refresh fails - the order was created successfully
      }

      // Dispatch event for other components
      if (typeof window !== 'undefined') {
        console.log('[OrderV4.createOrder] 📡 Dispatching order-created event...');
        window.dispatchEvent(new CustomEvent('order-created', { 
          detail: { order: localOrder } 
        }));
      }

      console.log('[OrderV4.createOrder] 🎉 Order creation process completed successfully');
      return localOrder;
    } catch (err) {
      console.error('[OrderV4.createOrder] ❌ Error creating order:', err);
      console.error('[OrderV4.createOrder] Error details:', {
        message: (err as any)?.message,
        status: (err as any)?.status,
        name: (err as any)?.name,
        stack: (err as any)?.stack
      });
      const error = err instanceof Error ? err : new Error('Failed to create order');
      setOrdersError(error);
      throw error;
    }
  }, [isHookReady, refreshOrders]);

  // Update an order
  const updateOrder = useCallback(async (orderId: string, updates: Partial<OrderDocument>): Promise<Order> => {
    if (!isHookReady) {
      const error = new Error('Order hook not ready (DB not initialized or user not authenticated)');
      setOrdersError(error);
      throw error;
    }

    try {
      const updatedOrder = await v4UpdateOrder(orderId, updates);
      
      // Convert to Order type with id field
      const updatedOrderWithId = toOrder(updatedOrder);
      
      // Update local state
      setOrders(prevOrders => {
        const index = prevOrders.findIndex(o => o.id === orderId);
        if (index !== -1) {
          const newOrders = [...prevOrders];
          newOrders[index] = updatedOrderWithId;
          return newOrders;
        }
        return prevOrders;
      });
      
      // Update active orders if needed
      setActiveOrders(prevActive => {
        const index = prevActive.findIndex(o => o.id === orderId);
        const isActive = updatedOrder.status === 'pending' || updatedOrder.status === 'preparing' || updatedOrder.status === 'served';
        if (!isActive) {
          // If order is now completed or cancelled, remove from active orders
          if (index !== -1) {
            const newActive = [...prevActive];
            newActive.splice(index, 1);
            return newActive;
          }
          return prevActive;
        }
        // Otherwise, update or add the order in active orders
        if (index !== -1) {
          const newActive = [...prevActive];
          newActive[index] = updatedOrderWithId;
          return newActive;
        } else {
          // Add to active orders if it wasn't there before and is now active
          return [updatedOrderWithId, ...prevActive];
        }
      });
      
      return updatedOrderWithId;
    } catch (err) {
      console.error(`❌ Error updating order ${orderId}:`, err);
      const error = err instanceof Error ? err : new Error('Failed to update order');
      setOrdersError(error);
      throw error;
    }
  }, [isHookReady]);

  // Get an order by ID
  const getOrder = useCallback(async (orderId: string): Promise<Order | null> => {
    if (!isHookReady) {
      console.warn('[OrderV4.getOrder] Skipped: Hook not ready.');
      return null;
    }

    try {
      const order = await v4GetOrder(orderId);
      return toOrder(order);
    } catch (err) {
      console.error(`❌ Error getting order ${orderId}:`, err);
      return null;
    }
  }, [isHookReady]);

  // Delete an order
  const deleteOrder = useCallback(async (orderId: string): Promise<void> => {
    if (!isHookReady) {
      const error = new Error('Order hook not ready (DB not initialized or user not authenticated)');
      setOrdersError(error);
      throw error;
    }

    try {
      await v4DeleteOrder(orderId);
      
      // Update local state
      setOrders(prevOrders => prevOrders.filter(o => o.id !== orderId));
      setActiveOrders(prevActive => prevActive.filter(o => o.id !== orderId));
    } catch (err) {
      console.error('❌ Error deleting order:', err);
      const error = err instanceof Error ? err : new Error('Failed to delete order');
      setOrdersError(error);
      throw error;
    }
  }, [isHookReady]);

  return {
    orders,
    activeOrders,
    isLoading: isLoadingDb || isOrdersLoading,
    error: dbInitializeError || ordersError,
    syncState,
    isReady: isDbInitialized && isHookReady && !isOrdersLoading,
    refreshOrders,
    createOrder,
    updateOrder,
    getOrder,
    deleteOrder
  };
}
