/**
 * Universal Seed Data for Restaurant Management System
 * 
 * This file contains standard restaurant data that provides a foundation
 * for any new restaurant setup. Includes basic menu items, recipes, and stock items.
 */

import { StockItem } from '../../../../types/stock';
import { SubRecipe } from '../schemas/sub-recipe-schema';
import { MenuDocument } from '../schemas/menu-schema';
import { MenuItemRecipe } from '../schemas/menu-item-recipe-schema';

// ============================================================================
// STOCK ITEMS - Basic ingredients and supplies
// ============================================================================

export const UNIVERSAL_STOCK_ITEMS: Omit<StockItem, 'createdAt' | 'updatedAt'>[] = [
  // Farines et Céréales
  {
    id: 'farine-ble',
    name: 'Farine de Blé T55',
    category: 'Farines et Céréales',
    unit: 'kg',
    minLevel: 25,
    costPerUnit: 85,
    quantity: 50,
    purchaseUnits: [
      {
        id: 'sac-25kg',
        name: 'Sac 25kg',
        conversionToBase: 25,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'semoule',
    name: 'Semoule Fine',
    category: 'Farines et Céréales',
    unit: 'kg',
    minLevel: 10,
    costPerUnit: 120,
    quantity: 25,
    purchaseUnits: [
      {
        id: 'sac-10kg',
        name: 'Sac 10kg',
        conversionToBase: 10,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  
  // Produits Laitiers
  {
    id: 'mozzarella',
    name: 'Mozzarella Pizza',
    category: 'Produits Laitiers',
    unit: 'kg',
    minLevel: 8,
    costPerUnit: 650,
    quantity: 20,
    purchaseUnits: [
      {
        id: 'bloc-2kg',
        name: 'Bloc 2kg',
        conversionToBase: 2,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'cheddar',
    name: 'Cheddar Affiné',
    category: 'Produits Laitiers',
    unit: 'kg',
    minLevel: 5,
    costPerUnit: 780,
    quantity: 10,
    purchaseUnits: [
      {
        id: 'bloc-1kg',
        name: 'Bloc 1kg',
        conversionToBase: 1,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'beurre',
    name: 'Beurre Doux',
    category: 'Produits Laitiers',
    unit: 'kg',
    minLevel: 3,
    costPerUnit: 450,
    quantity: 8,
    purchaseUnits: [
      {
        id: 'plaque-500g',
        name: 'Plaque 500g',
        conversionToBase: 0.5,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'lait',
    name: 'Lait Entier',
    category: 'Produits Laitiers',
    unit: 'L',
    minLevel: 10,
    costPerUnit: 95,
    quantity: 20,
    purchaseUnits: [
      {
        id: 'brique-1l',
        name: 'Brique 1L',
        conversionToBase: 1,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  
  // Légumes
  {
    id: 'tomates-fraiches',
    name: 'Tomates Fraîches',
    category: 'Légumes',
    unit: 'kg',
    minLevel: 8,
    costPerUnit: 220,
    quantity: 15,
    purchaseUnits: [
      {
        id: 'cagette-5kg',
        name: 'Cagette 5kg',
        conversionToBase: 5,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'concentre-tomate',
    name: 'Concentré de Tomate',
    category: 'Légumes',
    unit: 'kg',
    minLevel: 12,
    costPerUnit: 180,
    quantity: 24,
    purchaseUnits: [
      {
        id: 'boite-800g',
        name: 'Boîte 800g',
        conversionToBase: 0.8,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'oignons',
    name: 'Oignons Jaunes',
    category: 'Légumes',
    unit: 'kg',
    minLevel: 10,
    costPerUnit: 120,
    quantity: 20,
    purchaseUnits: [
      {
        id: 'filet-10kg',
        name: 'Filet 10kg',
        conversionToBase: 10,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'salade',
    name: 'Salade Iceberg',
    category: 'Légumes',
    unit: 'pcs',
    minLevel: 12,
    costPerUnit: 45,
    quantity: 24,
    purchaseUnits: [
      {
        id: 'piece',
        name: 'Pièce',
        conversionToBase: 1,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'poivrons',
    name: 'Poivrons Colorés',
    category: 'Légumes',
    unit: 'kg',
    minLevel: 5,
    costPerUnit: 380,
    quantity: 10,
    purchaseUnits: [
      {
        id: 'barquette-1kg',
        name: 'Barquette 1kg',
        conversionToBase: 1,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'champignons',
    name: 'Champignons de Paris',
    category: 'Légumes',
    unit: 'kg',
    minLevel: 4,
    costPerUnit: 420,
    quantity: 8,
    purchaseUnits: [
      {
        id: 'barquette-500g',
        name: 'Barquette 500g',
        conversionToBase: 0.5,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  
  // Viandes et Protéines
  {
    id: 'blanc-poulet',
    name: 'Blanc de Poulet',
    category: 'Viandes et Protéines',
    unit: 'kg',
    minLevel: 8,
    costPerUnit: 850,
    quantity: 15,
    purchaseUnits: [
      {
        id: 'barquette-1kg',
        name: 'Barquette 1kg',
        conversionToBase: 1,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'viande-hachee',
    name: 'Viande Hachée Bœuf',
    category: 'Viandes et Protéines',
    unit: 'kg',
    minLevel: 8,
    costPerUnit: 1200,
    quantity: 15,
    purchaseUnits: [
      {
        id: 'barquette-500g',
        name: 'Barquette 500g',
        conversionToBase: 0.5,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'pepperoni',
    name: 'Pepperoni Tranché',
    category: 'Viandes et Protéines',
    unit: 'kg',
    minLevel: 3,
    costPerUnit: 1450,
    quantity: 6,
    purchaseUnits: [
      {
        id: 'sachet-500g',
        name: 'Sachet 500g',
        conversionToBase: 0.5,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'jambon',
    name: 'Jambon de Dinde',
    category: 'Viandes et Protéines',
    unit: 'kg',
    minLevel: 3,
    costPerUnit: 980,
    quantity: 6,
    purchaseUnits: [
      {
        id: 'barquette-500g',
        name: 'Barquette 500g',
        conversionToBase: 0.5,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'thon',
    name: 'Thon à l\'Huile',
    category: 'Viandes et Protéines',
    unit: 'kg',
    minLevel: 6,
    costPerUnit: 320,
    quantity: 12,
    purchaseUnits: [
      {
        id: 'boite-160g',
        name: 'Boîte 160g',
        conversionToBase: 0.16,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  
  // Épices et Assaisonnements
  {
    id: 'sel',
    name: 'Sel Fin',
    category: 'Épices et Assaisonnements',
    unit: 'kg',
    minLevel: 2,
    costPerUnit: 80,
    quantity: 5,
    purchaseUnits: [
      {
        id: 'paquet-1kg',
        name: 'Paquet 1kg',
        conversionToBase: 1,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'poivre-noir',
    name: 'Poivre Noir Moulu',
    category: 'Épices et Assaisonnements',
    unit: 'g',
    minLevel: 200,
    costPerUnit: 2.5,
    quantity: 500,
    purchaseUnits: [
      {
        id: 'pot-100g',
        name: 'Pot 100g',
        conversionToBase: 100,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'origan',
    name: 'Origan Séché',
    category: 'Épices et Assaisonnements',
    unit: 'g',
    minLevel: 100,
    costPerUnit: 4,
    quantity: 250,
    purchaseUnits: [
      {
        id: 'pot-50g',
        name: 'Pot 50g',
        conversionToBase: 50,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'basilic',
    name: 'Basilic Séché',
    category: 'Épices et Assaisonnements',
    unit: 'g',
    minLevel: 100,
    costPerUnit: 5,
    quantity: 200,
    purchaseUnits: [
      {
        id: 'pot-40g',
        name: 'Pot 40g',
        conversionToBase: 40,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  
  // Huiles et Liquides
  {
    id: 'huile-olive',
    name: 'Huile d\'Olive Extra Vierge',
    category: 'Huiles et Liquides',
    unit: 'L',
    minLevel: 3,
    costPerUnit: 450,
    quantity: 8,
    purchaseUnits: [
      {
        id: 'bouteille-1l',
        name: 'Bouteille 1L',
        conversionToBase: 1,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'levure',
    name: 'Levure Boulangère Sèche',
    category: 'Boulangerie',
    unit: 'g',
    minLevel: 200,
    costPerUnit: 0.8,
    quantity: 500,
    purchaseUnits: [
      {
        id: 'sachet-11g',
        name: 'Sachet 11g',
        conversionToBase: 11,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  
  // Pain et Wraps
  {
    id: 'pain-sandwich',
    name: 'Pain de Mie',
    category: 'Pain et Wraps',
    unit: 'pcs',
    minLevel: 20,
    costPerUnit: 85,
    quantity: 40,
    purchaseUnits: [
      {
        id: 'paquet',
        name: 'Paquet',
        conversionToBase: 1,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'tortillas',
    name: 'Tortillas de Blé',
    category: 'Pain et Wraps',
    unit: 'pcs',
    minLevel: 50,
    costPerUnit: 15,
    quantity: 100,
    purchaseUnits: [
      {
        id: 'paquet-8pcs',
        name: 'Paquet 8 pièces',
        conversionToBase: 8,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  
  // Boissons
  {
    id: 'sirop-cola',
    name: 'Sirop de Cola',
    category: 'Boissons',
    unit: 'L',
    minLevel: 2,
    costPerUnit: 680,
    quantity: 5,
    purchaseUnits: [
      {
        id: 'bidon-5l',
        name: 'Bidon 5L',
        conversionToBase: 5,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'jus-orange',
    name: 'Jus d\'Orange',
    category: 'Boissons',
    unit: 'L',
    minLevel: 3,
    costPerUnit: 180,
    quantity: 12,
    purchaseUnits: [
      {
        id: 'brique-1l',
        name: 'Brique 1L',
        conversionToBase: 1,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'cafe-grains',
    name: 'Café en Grains',
    category: 'Boissons',
    unit: 'kg',
    minLevel: 1,
    costPerUnit: 1200,
    quantity: 3,
    purchaseUnits: [
      {
        id: 'paquet-1kg',
        name: 'Paquet 1kg',
        conversionToBase: 1,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  {
    id: 'eau-gazeuse',
    name: 'Eau Gazeuse',
    category: 'Boissons',
    unit: 'L',
    minLevel: 10,
    costPerUnit: 45,
    quantity: 24,
    purchaseUnits: [
      {
        id: 'bouteille-1l',
        name: 'Bouteille 1L',
        conversionToBase: 1,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  },
  
  // Packaging
  {
    id: 'pizza-box-medium',
    name: 'Pizza Box (Medium)',
    category: 'Packaging',
    unit: 'pcs',
    minLevel: 50,
    costPerUnit: 0.80
  },
  {
    id: 'pizza-box-large',
    name: 'Pizza Box (Large)',
    category: 'Packaging',
    unit: 'pcs',
    minLevel: 50,
    costPerUnit: 1.20
  },
  {
    id: 'takeaway-container',
    name: 'Takeaway Container',
    category: 'Packaging',
    unit: 'pcs',
    minLevel: 100,
    costPerUnit: 0.45
  },
  {
    id: 'paper-bag',
    name: 'Paper Bag',
    category: 'Packaging',
    unit: 'pcs',
    minLevel: 100,
    costPerUnit: 0.15
  },
  {
    id: 'drink-cup-medium',
    name: 'Drink Cup (Medium)',
    category: 'Packaging',
    unit: 'pcs',
    minLevel: 100,
    costPerUnit: 0.25
  }
];

// ============================================================================
// SUB-RECIPES - Common preparations
// ============================================================================

export const UNIVERSAL_SUB_RECIPES: Omit<SubRecipe, '_id' | 'createdAt' | 'updatedAt'>[] = [
  {
    id: 'pate-a-pizza',
    type: 'sub-recipe',
    name: 'Pâte à Pizza',
    ingredients: [
      { stockItemId: 'farine-ble', quantity: 1 },
      { stockItemId: 'huile-olive', quantity: 0.06 },
      { stockItemId: 'sel', quantity: 0.01 },
      { stockItemId: 'levure', quantity: 0.007 }
    ],
    yield: { quantity: 1.5, unit: 'kg' },
    costPerUnit: 185
  },
  {
    id: 'sauce-pizza',
    type: 'sub-recipe',
    name: 'Sauce Pizza',
    ingredients: [
      { stockItemId: 'concentre-tomate', quantity: 0.4 },
      { stockItemId: 'huile-olive', quantity: 0.03 },
      { stockItemId: 'origan', quantity: 0.005 },
      { stockItemId: 'basilic', quantity: 0.005 },
      { stockItemId: 'sel', quantity: 0.01 }
    ],
    yield: { quantity: 0.8, unit: 'kg' },
    costPerUnit: 165
  },
  {
    id: 'viande-hachee-assaisonnee',
    type: 'sub-recipe',
    name: 'Viande Hachée Assaisonnée',
    ingredients: [
      { stockItemId: 'viande-hachee', quantity: 1 },
      { stockItemId: 'oignons', quantity: 0.15 },
      { stockItemId: 'sel', quantity: 0.01 },
      { stockItemId: 'poivre-noir', quantity: 0.003 }
    ],
    yield: { quantity: 1.1, unit: 'kg' },
    costPerUnit: 1350
  },
  {
    id: 'blanc-de-poulet-grille',
    type: 'sub-recipe',
    name: 'Blanc de Poulet Grillé',
    ingredients: [
      { stockItemId: 'blanc-poulet', quantity: 1 },
      { stockItemId: 'huile-olive', quantity: 0.02 },
      { stockItemId: 'sel', quantity: 0.008 },
      { stockItemId: 'poivre-noir', quantity: 0.002 }
    ],
    yield: { quantity: 0.9, unit: 'kg' },
    costPerUnit: 920
  }
];

// ============================================================================
// MENU DOCUMENT - Categories and Items
// ============================================================================

export const UNIVERSAL_MENU_DOCUMENT: Omit<MenuDocument, '_rev' | 'createdAt' | 'updatedAt'> = {
  _id: 'menu',
  type: 'menu_document',
  schemaVersion: 'v4.0',
  categories: [
    // PIZZA CATEGORY
    {
      id: 'pizza',
      name: 'Pizza',
      emoji: '🍕',
      color: '#FF6B35',
      sizes: ['Medium', 'Large'],
      packaging: {
        'Medium': {
          'dine-in': [],
          'takeaway': [{ stockItemId: 'pizza-box-medium', quantity: 1 }],
          'delivery': [{ stockItemId: 'pizza-box-medium', quantity: 1 }]
        },
        'Large': {
          'dine-in': [],
          'takeaway': [{ stockItemId: 'pizza-box-large', quantity: 1 }],
          'delivery': [{ stockItemId: 'pizza-box-large', quantity: 1 }]
        }
      },
      isQuarterable: true,
      quarterPricingMethod: 'max',
      items: [
        {
          id: 'margherita',
          name: 'Margherita',
          description: 'Classic pizza with tomato sauce, mozzarella, and fresh basil',
          prices: { 'Medium': 890, 'Large': 1290 }
        },
        {
          id: 'pepperoni',
          name: 'Pepperoni',
          description: 'Tomato sauce, mozzarella, and premium pepperoni',
          prices: { 'Medium': 1090, 'Large': 1590 }
        },
        {
          id: 'supreme',
          name: 'Supreme',
          description: 'Loaded with pepperoni, mushrooms, bell peppers, and onions',
          prices: { 'Medium': 1390, 'Large': 1990 }
        },
        {
          id: 'meat-lovers',
          name: 'Meat Lovers',
          description: 'Pepperoni, ham, and seasoned ground beef',
          prices: { 'Medium': 1490, 'Large': 2190 }
        },
        {
          id: 'quattro-stagioni',
          name: 'Quattro Stagioni',
          description: 'Four seasons pizza with artichokes, mushrooms, ham, and olives',
          prices: { 'Medium': 1290, 'Large': 1890 }
        },
        {
          id: 'vegetarian',
          name: 'Vegetarian',
          description: 'Bell peppers, mushrooms, onions, tomatoes, and olives',
          prices: { 'Medium': 1190, 'Large': 1690 }
        }
      ]
    },
    
    // SANDWICHES CATEGORY
    {
      id: 'sandwiches',
      name: 'Sandwiches',
      emoji: '🥪',
      color: '#4ECDC4',
      sizes: ['Regular'],
      packaging: {
        'Regular': {
          'dine-in': [],
          'takeaway': [{ stockItemId: 'paper-bag', quantity: 1 }],
          'delivery': [{ stockItemId: 'takeaway-container', quantity: 1 }]
        }
      },
      items: [
        {
          id: 'chicken-sandwich',
          name: 'Grilled Chicken Sandwich',
          description: 'Grilled chicken breast with lettuce, tomato, and mayo',
          prices: { 'Regular': 650 }
        },
        {
          id: 'club-sandwich',
          name: 'Club Sandwich',
          description: 'Triple-layer with ham, chicken, lettuce, tomato, and cheese',
          prices: { 'Regular': 790 }
        },
        {
          id: 'cheese-sandwich',
          name: 'Grilled Cheese',
          description: 'Melted cheddar and mozzarella on toasted bread',
          prices: { 'Regular': 450 }
        },
        {
          id: 'blt-sandwich',
          name: 'BLT Sandwich',
          description: 'Bacon, lettuce, tomato with mayo on toasted bread',
          prices: { 'Regular': 590 }
        },
        {
          id: 'tuna-sandwich',
          name: 'Tuna Melt',
          description: 'Tuna salad with melted cheese on grilled bread',
          prices: { 'Regular': 690 }
        }
      ]
    },
    
    // TACOS CATEGORY
    {
      id: 'tacos',
      name: 'Tacos',
      emoji: '🌮',
      color: '#FFE66D',
      sizes: ['Single', '3-Pack'],
      packaging: {
        'Single': {
          'dine-in': [],
          'takeaway': [{ stockItemId: 'paper-bag', quantity: 1 }],
          'delivery': [{ stockItemId: 'takeaway-container', quantity: 1 }]
        },
        '3-Pack': {
          'dine-in': [],
          'takeaway': [{ stockItemId: 'takeaway-container', quantity: 1 }],
          'delivery': [{ stockItemId: 'takeaway-container', quantity: 1 }]
        }
      },
      items: [
        {
          id: 'beef-taco',
          name: 'Beef Taco',
          description: 'Seasoned ground beef with lettuce, cheese, and salsa',
          prices: { 'Single': 290, '3-Pack': 790 }
        },
        {
          id: 'chicken-taco',
          name: 'Chicken Taco',
          description: 'Grilled chicken strips with lettuce, cheese, and salsa',
          prices: { 'Single': 320, '3-Pack': 890 }
        },
        {
          id: 'veggie-taco',
          name: 'Veggie Taco',
          description: 'Bell peppers, mushrooms, onions, cheese, and salsa',
          prices: { 'Single': 250, '3-Pack': 690 }
        },
        {
          id: 'fish-taco',
          name: 'Fish Taco',
          description: 'Grilled fish with cabbage slaw and chipotle sauce',
          prices: { 'Single': 350, '3-Pack': 990 }
        }
      ]
    },
    
    // DRINKS CATEGORY
    {
      id: 'drinks',
      name: 'Drinks',
      emoji: '🥤',
      color: '#A8E6CF',
      sizes: ['Small', 'Medium', 'Large'],
      packaging: {
        'Small': {
          'dine-in': [],
          'takeaway': [{ stockItemId: 'drink-cup-medium', quantity: 1 }],
          'delivery': [{ stockItemId: 'drink-cup-medium', quantity: 1 }]
        },
        'Medium': {
          'dine-in': [],
          'takeaway': [{ stockItemId: 'drink-cup-medium', quantity: 1 }],
          'delivery': [{ stockItemId: 'drink-cup-medium', quantity: 1 }]
        },
        'Large': {
          'dine-in': [],
          'takeaway': [{ stockItemId: 'drink-cup-medium', quantity: 1 }],
          'delivery': [{ stockItemId: 'drink-cup-medium', quantity: 1 }]
        }
      },
      items: [
        {
          id: 'cola',
          name: 'Cola',
          description: 'Classic cola soft drink',
          prices: { 'Small': 180, 'Medium': 220, 'Large': 280 }
        },
        {
          id: 'orange-juice',
          name: 'Orange Juice',
          description: 'Fresh squeezed orange juice',
          prices: { 'Small': 250, 'Medium': 320, 'Large': 390 }
        },
        {
          id: 'coffee',
          name: 'Coffee',
          description: 'Freshly brewed arabica coffee',
          prices: { 'Small': 200, 'Medium': 250, 'Large': 320 }
        },
        {
          id: 'water',
          name: 'Bottled Water',
          description: 'Premium bottled water',
          prices: { 'Small': 120, 'Medium': 150, 'Large': 180 }
        },
        {
          id: 'lemonade',
          name: 'Fresh Lemonade',
          description: 'House-made fresh lemonade',
          prices: { 'Small': 220, 'Medium': 280, 'Large': 350 }
        },
        {
          id: 'iced-tea',
          name: 'Iced Tea',
          description: 'Refreshing iced tea with lemon',
          prices: { 'Small': 190, 'Medium': 240, 'Large': 300 }
        }
      ]
    }
  ]
};

// ============================================================================
// MENU ITEM RECIPES - How to make each item
// ============================================================================

export const UNIVERSAL_MENU_ITEM_RECIPES: Omit<MenuItemRecipe, '_id' | '_rev' | 'createdAt' | 'updatedAt'>[] = [
  // PIZZA RECIPES
  {
    type: 'menu-item-recipe',
    menuItemId: 'margherita',
    menuItemName: 'Margherita',
    size: 'Medium',
    ingredients: [
      { subRecipeId: 'pate-a-pizza', quantity: 0.25 },
      { subRecipeId: 'sauce-pizza', quantity: 0.08 },
      { stockItemId: 'mozzarella', quantity: 0.12 },
      { stockItemId: 'basilic', quantity: 0.002 }
    ],
    costPerUnit: 285
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'margherita',
    menuItemName: 'Margherita',
    size: 'Large',
    ingredients: [
      { subRecipeId: 'pate-a-pizza', quantity: 0.4 },
      { subRecipeId: 'sauce-pizza', quantity: 0.12 },
      { stockItemId: 'mozzarella', quantity: 0.18 },
      { stockItemId: 'basilic', quantity: 0.003 }
    ],
    costPerUnit: 420
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'pepperoni',
    menuItemName: 'Pepperoni',
    size: 'Medium',
    ingredients: [
      { subRecipeId: 'pate-a-pizza', quantity: 0.25 },
      { subRecipeId: 'sauce-pizza', quantity: 0.08 },
      { stockItemId: 'mozzarella', quantity: 0.12 },
      { stockItemId: 'pepperoni', quantity: 0.06 }
    ],
    costPerUnit: 395
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'pepperoni',
    menuItemName: 'Pepperoni',
    size: 'Large',
    ingredients: [
      { subRecipeId: 'pate-a-pizza', quantity: 0.4 },
      { subRecipeId: 'sauce-pizza', quantity: 0.12 },
      { stockItemId: 'mozzarella', quantity: 0.18 },
      { stockItemId: 'pepperoni', quantity: 0.09 }
    ],
    costPerUnit: 580
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'supreme',
    menuItemName: 'Supreme',
    size: 'Medium',
    ingredients: [
      { subRecipeId: 'pate-a-pizza', quantity: 0.25 },
      { subRecipeId: 'sauce-pizza', quantity: 0.08 },
      { stockItemId: 'mozzarella', quantity: 0.12 },
      { stockItemId: 'pepperoni', quantity: 0.04 },
      { stockItemId: 'champignons', quantity: 0.03 },
      { stockItemId: 'poivrons', quantity: 0.03 },
      { stockItemId: 'oignons', quantity: 0.02 }
    ],
    costPerUnit: 485
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'supreme',
    menuItemName: 'Supreme',
    size: 'Large',
    ingredients: [
      { subRecipeId: 'pate-a-pizza', quantity: 0.4 },
      { subRecipeId: 'sauce-pizza', quantity: 0.12 },
      { stockItemId: 'mozzarella', quantity: 0.18 },
      { stockItemId: 'pepperoni', quantity: 0.06 },
      { stockItemId: 'champignons', quantity: 0.05 },
      { stockItemId: 'poivrons', quantity: 0.04 },
      { stockItemId: 'oignons', quantity: 0.03 }
    ],
    costPerUnit: 720
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'meat-lovers',
    menuItemName: 'Meat Lovers',
    size: 'Medium',
    ingredients: [
      { subRecipeId: 'pate-a-pizza', quantity: 0.25 },
      { subRecipeId: 'sauce-pizza', quantity: 0.08 },
      { stockItemId: 'mozzarella', quantity: 0.15 },
      { stockItemId: 'pepperoni', quantity: 0.06 },
      { stockItemId: 'jambon', quantity: 0.06 },
      { subRecipeId: 'viande-hachee-assaisonnee', quantity: 0.08 }
    ],
    costPerUnit: 595
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'meat-lovers',
    menuItemName: 'Meat Lovers',
    size: 'Large',
    ingredients: [
      { subRecipeId: 'pate-a-pizza', quantity: 0.4 },
      { subRecipeId: 'sauce-pizza', quantity: 0.12 },
      { stockItemId: 'mozzarella', quantity: 0.25 },
      { stockItemId: 'pepperoni', quantity: 0.1 },
      { stockItemId: 'jambon', quantity: 0.1 },
      { subRecipeId: 'viande-hachee-assaisonnee', quantity: 0.12 }
    ],
    costPerUnit: 890
  },

  // SANDWICH RECIPES
  {
    type: 'menu-item-recipe',
    menuItemId: 'chicken-sandwich',
    menuItemName: 'Grilled Chicken Sandwich',
    size: 'Regular',
    ingredients: [
      { stockItemId: 'pain-sandwich', quantity: 2 },
      { subRecipeId: 'blanc-de-poulet-grille', quantity: 0.15 },
      { stockItemId: 'salade', quantity: 0.03 },
      { stockItemId: 'tomates-fraiches', quantity: 0.05 },
      { stockItemId: 'beurre', quantity: 0.01 }
    ],
    costPerUnit: 245
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'club-sandwich',
    menuItemName: 'Club Sandwich',
    size: 'Regular',
    ingredients: [
      { stockItemId: 'pain-sandwich', quantity: 3 },
      { stockItemId: 'jambon', quantity: 0.08 },
      { subRecipeId: 'blanc-de-poulet-grille', quantity: 0.1 },
      { stockItemId: 'salade', quantity: 0.03 },
      { stockItemId: 'tomates-fraiches', quantity: 0.05 },
      { stockItemId: 'cheddar', quantity: 0.03 },
      { stockItemId: 'beurre', quantity: 0.015 }
    ],
    costPerUnit: 385
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'cheese-sandwich',
    menuItemName: 'Grilled Cheese',
    size: 'Regular',
    ingredients: [
      { stockItemId: 'pain-sandwich', quantity: 2 },
      { stockItemId: 'cheddar', quantity: 0.06 },
      { stockItemId: 'mozzarella', quantity: 0.04 },
      { stockItemId: 'beurre', quantity: 0.01 }
    ],
    costPerUnit: 185
  },

  // TACO RECIPES
  {
    type: 'menu-item-recipe',
    menuItemId: 'beef-taco',
    menuItemName: 'Beef Taco',
    size: 'Single',
    ingredients: [
      { stockItemId: 'tortillas', quantity: 1 },
      { subRecipeId: 'viande-hachee-assaisonnee', quantity: 0.08 },
      { stockItemId: 'salade', quantity: 0.02 },
      { stockItemId: 'cheddar', quantity: 0.02 }
    ],
    costPerUnit: 125
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'beef-taco',
    menuItemName: 'Beef Taco',
    size: '3-Pack',
    ingredients: [
      { stockItemId: 'tortillas', quantity: 3 },
      { subRecipeId: 'viande-hachee-assaisonnee', quantity: 0.24 },
      { stockItemId: 'salade', quantity: 0.06 },
      { stockItemId: 'cheddar', quantity: 0.06 }
    ],
    costPerUnit: 375
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'chicken-taco',
    menuItemName: 'Chicken Taco',
    size: 'Single',
    ingredients: [
      { stockItemId: 'tortillas', quantity: 1 },
      { subRecipeId: 'blanc-de-poulet-grille', quantity: 0.08 },
      { stockItemId: 'salade', quantity: 0.02 },
      { stockItemId: 'cheddar', quantity: 0.02 }
    ],
    costPerUnit: 145
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'chicken-taco',
    menuItemName: 'Chicken Taco',
    size: '3-Pack',
    ingredients: [
      { stockItemId: 'tortillas', quantity: 3 },
      { subRecipeId: 'blanc-de-poulet-grille', quantity: 0.24 },
      { stockItemId: 'salade', quantity: 0.06 },
      { stockItemId: 'cheddar', quantity: 0.06 }
    ],
    costPerUnit: 435
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'veggie-taco',
    menuItemName: 'Veggie Taco',
    size: 'Single',
    ingredients: [
      { stockItemId: 'tortillas', quantity: 1 },
      { stockItemId: 'poivrons', quantity: 0.04 },
      { stockItemId: 'champignons', quantity: 0.03 },
      { stockItemId: 'oignons', quantity: 0.02 },
      { stockItemId: 'cheddar', quantity: 0.02 }
    ],
    costPerUnit: 95
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'veggie-taco',
    menuItemName: 'Veggie Taco',
    size: '3-Pack',
    ingredients: [
      { stockItemId: 'tortillas', quantity: 3 },
      { stockItemId: 'poivrons', quantity: 0.12 },
      { stockItemId: 'champignons', quantity: 0.09 },
      { stockItemId: 'oignons', quantity: 0.06 },
      { stockItemId: 'cheddar', quantity: 0.06 }
    ],
    costPerUnit: 285
  },

  // DRINK RECIPES
  {
    type: 'menu-item-recipe',
    menuItemId: 'cola',
    menuItemName: 'Cola',
    size: 'Small',
    ingredients: [
      { stockItemId: 'sirop-cola', quantity: 0.05 },
      { stockItemId: 'eau-gazeuse', quantity: 0.25 }
    ],
    costPerUnit: 45
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'cola',
    menuItemName: 'Cola',
    size: 'Medium',
    ingredients: [
      { stockItemId: 'sirop-cola', quantity: 0.08 },
      { stockItemId: 'eau-gazeuse', quantity: 0.4 }
    ],
    costPerUnit: 72
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'cola',
    menuItemName: 'Cola',
    size: 'Large',
    ingredients: [
      { stockItemId: 'sirop-cola', quantity: 0.12 },
      { stockItemId: 'eau-gazeuse', quantity: 0.6 }
    ],
    costPerUnit: 108
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'orange-juice',
    menuItemName: 'Orange Juice',
    size: 'Small',
    ingredients: [
      { stockItemId: 'jus-orange', quantity: 0.25 }
    ],
    costPerUnit: 45
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'orange-juice',
    menuItemName: 'Orange Juice',
    size: 'Medium',
    ingredients: [
      { stockItemId: 'jus-orange', quantity: 0.4 }
    ],
    costPerUnit: 72
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'orange-juice',
    menuItemName: 'Orange Juice',
    size: 'Large',
    ingredients: [
      { stockItemId: 'jus-orange', quantity: 0.6 }
    ],
    costPerUnit: 108
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'coffee',
    menuItemName: 'Coffee',
    size: 'Small',
    ingredients: [
      { stockItemId: 'cafe-grains', quantity: 0.015 },
      { stockItemId: 'lait', quantity: 0.05 }
    ],
    costPerUnit: 23
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'coffee',
    menuItemName: 'Coffee',
    size: 'Medium',
    ingredients: [
      { stockItemId: 'cafe-grains', quantity: 0.02 },
      { stockItemId: 'lait', quantity: 0.08 }
    ],
    costPerUnit: 32
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'coffee',
    menuItemName: 'Coffee',
    size: 'Large',
    ingredients: [
      { stockItemId: 'cafe-grains', quantity: 0.025 },
      { stockItemId: 'lait', quantity: 0.1 }
    ],
    costPerUnit: 40
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'water',
    menuItemName: 'Bottled Water',
    size: 'Small',
    ingredients: [
      { stockItemId: 'eau-gazeuse', quantity: 0.33 }
    ],
    costPerUnit: 15
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'water',
    menuItemName: 'Bottled Water',
    size: 'Medium',
    ingredients: [
      { stockItemId: 'eau-gazeuse', quantity: 0.5 }
    ],
    costPerUnit: 23
  },
  {
    type: 'menu-item-recipe',
    menuItemId: 'water',
    menuItemName: 'Bottled Water',
    size: 'Large',
    ingredients: [
      { stockItemId: 'eau-gazeuse', quantity: 0.75 }
    ],
    costPerUnit: 34
  }
];

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Generate unique IDs for seed data with timestamps
 */
export function generateSeedId(prefix: string): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Add timestamps to seed data
 */
export function addTimestamps<T>(item: T): T & { createdAt: string; updatedAt: string } {
  const now = new Date().toISOString();
  return {
    ...item,
    createdAt: now,
    updatedAt: now
  };
}

/**
 * Prepare stock items with proper IDs and timestamps
 */
export function prepareStockItems(): StockItem[] {
  return UNIVERSAL_STOCK_ITEMS.map(item => addTimestamps(item));
}

/**
 * Prepare sub-recipes with proper IDs and timestamps
 */
export function prepareSubRecipes(): SubRecipe[] {
  return UNIVERSAL_SUB_RECIPES.map(recipe => ({
    ...addTimestamps(recipe),
    _id: generateSeedId('sub-recipe')
  }));
}

/**
 * Prepare menu document with timestamps
 */
export function prepareMenuDocument(): MenuDocument {
  return {
    ...UNIVERSAL_MENU_DOCUMENT,
    ...addTimestamps(UNIVERSAL_MENU_DOCUMENT)
  };
}

/**
 * Prepare menu item recipes with proper IDs and timestamps
 */
export function prepareMenuItemRecipes(): MenuItemRecipe[] {
  return UNIVERSAL_MENU_ITEM_RECIPES.map(recipe => ({
    ...addTimestamps(recipe),
    _id: generateSeedId('menu-item-recipe')
  }));
}