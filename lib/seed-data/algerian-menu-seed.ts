import { v4 as uuidv4 } from 'uuid';
import { PRESET_COLORS } from '../constants';

// Helper function to create unique IDs
const createId = () => uuidv4();

// 🍕 UNIVERSAL SEED - Optimized for Algerian Pizzeria Market
// All names in French, prices in Algerian Dinars (DA)

// Define pizza sizes with Algerian market standards
export const pizzaSizes = ['Normale', 'Mega', 'Géante'];

// Define drink sizes
export const drinkSizes = ['33cl', '50cl', '1.5L'];

// Define sandwich sizes  
export const sandwichSizes = ['Simple', 'Double'];

// Define supplement pricing by size
export const supplementPricing = {
  'Normale': { base: 50, premium: 80 },
  'Mega': { base: 75, premium: 120 },
  'Géante': { base: 100, premium: 150 }
};

// 🍕 MINIMAL PIZZERIA MENU
export const algerianMenu = [
  {
    id: createId(),
    name: 'Pizzas',
    emoji: '🍕',
    color: PRESET_COLORS[0],
    sizes: pizzaSizes,
    items: [
      {
        id: createId(),
        name: '<PERSON><PERSON><PERSON><PERSON>',
        description: 'Sauce tomate, mozzarella, basilic',
        prices: {
          'Normale': 500,
          'Mega': 800,
          'Géante': 1200
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Quatre Fromages',
        description: 'Mozzarella, emmental, chèvre, gruyère',
        prices: {
          'Normale': 600,
          'Mega': 900,
          'Géante': 1400
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Végétarienne',
        description: 'Poivrons, champignons, olives, tomates',
        prices: {
          'Normale': 550,
          'Mega': 850,
          'Géante': 1300
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Poulet',
        description: 'Poulet, champignons, mozzarella',
        prices: {
          'Normale': 650,
          'Mega': 1000,
          'Géante': 1500
        },
        color: PRESET_COLORS[3]
      },
      {
        id: createId(),
        name: 'Pepperoni',
        description: 'Pepperoni, mozzarella, sauce tomate',
        prices: {
          'Normale': 700,
          'Mega': 1100,
          'Géante': 1600
        },
        color: PRESET_COLORS[4]
      },
      {
        id: createId(),
        name: 'Mixte',
        description: 'Poulet, pepperoni, champignons, fromages',
        prices: {
          'Normale': 800,
          'Mega': 1300,
          'Géante': 2000
        },
        color: PRESET_COLORS[0]
      }
    ],
    // Pizza supplements configuration
    supplements: [
      {
        id: createId(),
        name: 'Extra Fromage',
        description: 'Double portion de fromage',
        isActive: true
      },
      {
        id: createId(),
        name: 'Olives Noires',
        description: 'Olives noires de Kalamata',
        isActive: true
      },
      {
        id: createId(),
        name: 'Champignons',
        description: 'Champignons de Paris frais',
        isActive: true
      },
      {
        id: createId(),
        name: 'Poivrons',
        description: 'Poivrons colorés grillés',
        isActive: true
      }
    ],
    supplementConfig: {
      globalPricing: {
        'Normale': 50,
        'Mega': 75,
        'Géante': 100
      },
      isEnabled: true
    }
  },

  {
    id: createId(),
    name: 'Boissons',
    emoji: '🥤',
    color: PRESET_COLORS[1],
    sizes: drinkSizes,
    items: [
      {
        id: createId(),
        name: 'Coca',
        description: 'Boisson gazeuse',
        prices: {
          '33cl': 50,
          '50cl': 80,
          '1.5L': 150
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Pepsi',
        description: 'Boisson gazeuse',
        prices: {
          '33cl': 50,
          '50cl': 80,
          '1.5L': 150
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Fanta',
        description: 'Boisson gazeuse orange',
        prices: {
          '33cl': 45,
          '50cl': 75,
          '1.5L': 140
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Sprite',
        description: 'Boisson gazeuse citron',
        prices: {
          '33cl': 45,
          '50cl': 75,
          '1.5L': 140
        },
        color: PRESET_COLORS[3]
      },
      {
        id: createId(),
        name: 'Eau',
        description: 'Eau minérale',
        prices: {
          '33cl': 30,
          '50cl': 50,
          '1.5L': 100
        },
        color: PRESET_COLORS[4]
      },
      {
        id: createId(),
        name: 'Jus Orange',
        description: 'Jus d\'orange frais',
        prices: {
          '33cl': 80,
          '50cl': 130,
          '1.5L': 250
        },
        color: PRESET_COLORS[0]
      }
    ]
  }

];
