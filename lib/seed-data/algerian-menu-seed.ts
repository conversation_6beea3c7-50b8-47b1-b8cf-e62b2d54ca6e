import { v4 as uuidv4 } from 'uuid';
import { PRESET_COLORS } from '../constants';

// Helper function to create unique IDs
const createId = () => uuidv4();

// 🍕 UNIVERSAL SEED - Optimized for Algerian Pizzeria Market
// All names in French, prices in Algerian Dinars (DA)

// Define pizza sizes with Algerian market standards
export const pizzaSizes = ['Normale', 'Mega', 'Géante'];

// Define drink sizes
export const drinkSizes = ['33cl', '50cl', '1.5L'];

// Define sandwich sizes  
export const sandwichSizes = ['Simple', 'Double'];

// Define supplement pricing by size
export const supplementPricing = {
  'Normale': { base: 50, premium: 80 },
  'Mega': { base: 75, premium: 120 },
  'Géante': { base: 100, premium: 150 }
};

// 🍕 COMPREHENSIVE ALGERIAN PIZZERIA MENU
export const algerianMenu = [
  {
    id: createId(),
    name: 'Pizzas Classiques',
    emoji: '🍕',
    color: PRESET_COLORS[0],
    sizes: pizzaSizes,
    items: [
      {
        id: createId(),
        name: 'Margherita Classique',
        description: 'Sauce tomate San Marzano, mozzarella di bufala, basilic frais, huile d\'olive extra vierge',
        prices: {
          'Normale': 520,
          'Mega': 750,
          'Géante': 980
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Quatre Fromages Premium',
        description: 'Mozzarella di bufala, emmental, chèvre frais, gruyère suisse, crème fraîche',
        prices: {
          'Normale': 720,
          'Mega': 950,
          'Géante': 1200
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Végétarienne Gourmet',
        description: 'Poivrons tricolores grillés, champignons de Paris, olives Kalamata, tomates cerises, roquette',
        prices: {
          'Normale': 580,
          'Mega': 780,
          'Géante': 1020
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Regina Royale',
        description: 'Jambon de dinde halal, champignons émincés, mozzarella, olives noires, origan',
        prices: {
          'Normale': 650,
          'Mega': 850,
          'Géante': 1100
        },
        color: PRESET_COLORS[3]
      },
      {
        id: createId(),
        name: 'Pepperoni Authentique',
        description: 'Pepperoni halal tranché, mozzarella di bufala, sauce tomate, parmesan râpé',
        prices: {
          'Normale': 680,
          'Mega': 880,
          'Géante': 1150
        },
        color: PRESET_COLORS[4]
      },
      {
        id: createId(),
        name: 'Napolitaine Traditionnelle',
        description: 'Anchois à l\'huile, câpres, olives noires, tomates cerises, mozzarella, origan',
        prices: {
          'Normale': 620,
          'Mega': 820,
          'Géante': 1080
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Hawaienne Revisitée',
        description: 'Jambon de dinde halal, ananas frais, mozzarella, sauce blanche crème',
        prices: {
          'Normale': 590,
          'Mega': 790,
          'Géante': 1050
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Calzone Classique',
        description: 'Pizza fermée: jambon halal, champignons, mozzarella, sauce tomate, œuf',
        prices: {
          'Normale': 640,
          'Mega': 840,
          'Géante': 1120
        },
        color: PRESET_COLORS[2]
      }
    ],
    // Pizza supplements configuration
    supplements: [
      {
        id: createId(),
        name: 'Extra Fromage',
        description: 'Double portion de fromage',
        isActive: true
      },
      {
        id: createId(),
        name: 'Olives Noires',
        description: 'Olives noires de Kalamata',
        isActive: true
      },
      {
        id: createId(),
        name: 'Champignons',
        description: 'Champignons de Paris frais',
        isActive: true
      },
      {
        id: createId(),
        name: 'Poivrons',
        description: 'Poivrons colorés grillés',
        isActive: true
      }
    ],
    supplementConfig: {
      globalPricing: {
        'Normale': 50,
        'Mega': 75,
        'Géante': 100
      },
      isEnabled: true
    }
  },
  {
    id: createId(),
    name: 'Pizzas Spéciales',
    emoji: '🌟',
    color: PRESET_COLORS[1],
    sizes: pizzaSizes,
    items: [
      {
        id: createId(),
        name: 'Algérienne Authentique',
        description: 'Merguez artisanales, poivrons grillés, harissa traditionnelle, mozzarella, oignons rouges',
        prices: {
          'Normale': 750,
          'Mega': 980,
          'Géante': 1250
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Orientale Délice',
        description: 'Escalope de poulet marinée, olives Kalamata, tomates cerises, mélange d\'épices, roquette',
        prices: {
          'Normale': 720,
          'Mega': 920,
          'Géante': 1180
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Royal du Chef',
        description: 'Viande hachée halal 15% MG, merguez, escalope poulet, trio de fromages, champignons',
        prices: {
          'Normale': 850,
          'Mega': 1100,
          'Géante': 1380
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Méditerranéenne Premium',
        description: 'Thon à l\'huile d\'olive, anchois, olives noires, câpres, tomates cerises, roquette fraîche',
        prices: {
          'Normale': 680,
          'Mega': 880,
          'Géante': 1150
        },
        color: PRESET_COLORS[3]
      },
      {
        id: createId(),
        name: 'Tunisienne Epicee',
        description: 'Kefta épicée maison, harissa, poivrons, olives, fromage de chèvre, menthe fraîche',
        prices: {
          'Normale': 780,
          'Mega': 1020,
          'Géante': 1320
        },
        color: PRESET_COLORS[4]
      },
      {
        id: createId(),
        name: 'Berbere du Sahara',
        description: 'Viande d\'agneau épicée, dattes fraîches, amandes, fromage de chèvre, miel',
        prices: {
          'Normale': 920,
          'Mega': 1200,
          'Géante': 1520
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Atlas Special',
        description: 'Trio de viandes (merguez, poulet, kefta), trois fromages, légumes grillés',
        prices: {
          'Normale': 890,
          'Mega': 1150,
          'Géante': 1450
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Pesto Genovese',
        description: 'Pesto basilic maison, mozzarella di bufala, tomates cerises, pignons de pin, parmesan',
        prices: {
          'Normale': 760,
          'Mega': 980,
          'Géante': 1280
        },
        color: PRESET_COLORS[2]
      }
    ]
  },
  {
    id: createId(),
    name: 'Sandwiches & Wraps',
    emoji: '🥪',
    color: PRESET_COLORS[2],
    sizes: sandwichSizes,
    items: [
      {
        id: createId(),
        name: 'Kebab d\'Agneau Traditionnel',
        description: 'Viande d\'agneau marinée 24h, salade mixte, tomates, oignons, sauce blanche maison, pain artisanal',
        prices: {
          'Simple': 420,
          'Double': 650
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Escalope de Poulet Marinée',
        description: 'Escalope poulet aux épices, crudités fraîches, sauce algérienne, frites maison',
        prices: {
          'Simple': 450,
          'Double': 680
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Merguez Artisanales',
        description: 'Merguez grillées artisanales, frites croustillantes, harissa traditionnelle, salade',
        prices: {
          'Simple': 380,
          'Double': 580
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Kefta Maison aux Epices',
        description: 'Boulettes de viande épicées fait maison, légumes grillés, sauce tomate, pain pita',
        prices: {
          'Simple': 480,
          'Double': 720
        },
        color: PRESET_COLORS[3]
      },
      {
        id: createId(),
        name: 'Thon à l\'Olive Premium',
        description: 'Thon à l\'huile d\'olive, mayonnaise maison, œuf dur, salade, tomates, olives',
        prices: {
          'Simple': 350,
          'Double': 520
        },
        color: PRESET_COLORS[4]
      },
      {
        id: createId(),
        name: 'Chawarma de Boeuf',
        description: 'Lamelles de bœuf marinées, légumes croquants, sauce tahini, pain libanais',
        prices: {
          'Simple': 520,
          'Double': 780
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Falafel Végétarien',
        description: 'Boulettes de pois chiches aux herbes, taboulé, houmous, pain pita, légumes frais',
        prices: {
          'Simple': 380,
          'Double': 560
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Mixte Grill Royal',
        description: 'Trio de viandes: kebab, merguez, kefta, frites, salade complète, sauces variées',
        prices: {
          'Simple': 650,
          'Double': 950
        },
        color: PRESET_COLORS[2]
      }
    ]
  },
  {
    id: createId(),
    name: 'Boissons',
    emoji: '🥤',
    color: PRESET_COLORS[3],
    sizes: drinkSizes,
    items: [
      {
        id: createId(),
        name: 'Coca-Cola Original',
        description: 'La boisson gazeuse iconique au goût unique et rafraîchissant',
        prices: {
          '33cl': 150,
          '50cl': 220,
          '1.5L': 420
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Fanta Orange Premium',
        description: 'Boisson gazeuse à l\'orange avec goût authentique d\'agrumes',
        prices: {
          '33cl': 140,
          '50cl': 210,
          '1.5L': 400
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Sprite Citron-Lime',
        description: 'Boisson gazeuse rafraîchissante au goût citron-lime naturel',
        prices: {
          '33cl': 140,
          '50cl': 210,
          '1.5L': 400
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Eau Minérale Saïda',
        description: 'Eau minérale naturelle des sources algériennes, pure et rafraîchissante',
        prices: {
          '33cl': 100,
          '50cl': 140,
          '1.5L': 250
        },
        color: PRESET_COLORS[3]
      },
      {
        id: createId(),
        name: 'Thé à la Menthe Traditionnel',
        description: 'Thé vert à la menthe fraîche, préparé selon la tradition algérienne',
        prices: {
          '33cl': 120,
          '50cl': 180,
          '1.5L': 320
        },
        color: PRESET_COLORS[4]
      },
      {
        id: createId(),
        name: 'Jus d\'Orange 100% Frais',
        description: 'Jus d\'orange pressé à la minute avec oranges fraîches locales',
        prices: {
          '33cl': 280,
          '50cl': 420,
          '1.5L': 750
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Limonade Artisanale',
        description: 'Limonade maison préparée avec citrons frais et menthe',
        prices: {
          '33cl': 200,
          '50cl': 300,
          '1.5L': 550
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Jus de Citronnade',
        description: 'Boisson rafraîchissante au citron avec une pointe de menthe',
        prices: {
          '33cl': 180,
          '50cl': 270,
          '1.5L': 480
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Ayran Traditionnel',
        description: 'Boisson lactée salée traditionnelle aux herbes, parfaite avec les plats épicés',
        prices: {
          '33cl': 160,
          '50cl': 240
        },
        color: PRESET_COLORS[3]
      }
    ]
  },
  {
    id: createId(),
    name: 'Entrées & Salades',
    emoji: '🥗',
    color: PRESET_COLORS[5] || PRESET_COLORS[0],
    items: [
      {
        id: createId(),
        name: 'Salade Méditerranéenne',
        description: 'Salade mixte, tomates cerises, olives, feta, concombre, vinaigrette à l\'huile d\'olive',
        prices: {
          'Portion': 380
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Houmous Traditionnel',
        description: 'Crème de pois chiches, tahini, citron, huile d\'olive, paprika, pain pita chaud',
        prices: {
          'Portion': 320
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Taboulé Libanais',
        description: 'Persil, tomates, menthe, boulgour, citron, huile d\'olive extra vierge',
        prices: {
          'Portion': 350
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Assiette de Mezze',
        description: 'Houmous, taboulé, baba ghanoush, olives, fromage blanc aux herbes, pain',
        prices: {
          'Pour 2 personnes': 850
        },
        color: PRESET_COLORS[3]
      }
    ]
  },
  {
    id: createId(),
    name: 'Desserts & Douceurs',
    emoji: '🍰',
    color: PRESET_COLORS[4],
    items: [
      {
        id: createId(),
        name: 'Baklawa aux Pistaches',
        description: 'Pâtisserie orientale feuilletée au miel d\'acacia et pistaches de Sicile, préparée artisanalement',
        prices: {
          'Portion': 320,
          'Plateau 6 pièces': 1800
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Makroudh aux Dattes Deglet Nour',
        description: 'Gâteau traditionnel de semoule farci aux dattes Deglet Nour, sirop de miel',
        prices: {
          'Portion': 280,
          'Plateau 4 pièces': 1000
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Tiramisu Authentique',
        description: 'Dessert italien au mascarpone, café expresso, cacao amer, savoiardis maison',
        prices: {
          'Portion': 380,
          'Part Géante': 520
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Glaces Artisanales',
        description: 'Glaces préparées maison: vanille Bourbon, chocolat noir, pistache, fraise',
        prices: {
          '2 Boules': 220,
          '3 Boules': 320,
          'Coupe Royale': 450
        },
        color: PRESET_COLORS[3]
      },
      {
        id: createId(),
        name: 'Chebakia au Miel',
        description: 'Pâtisserie marocaine en forme de rose, pate feuilletee au miel et graines de sesame',
        prices: {
          'Portion': 250,
          'Plateau 6 pièces': 1400
        },
        color: PRESET_COLORS[4]
      },
      {
        id: createId(),
        name: 'Mhalabia à la Fleur d\'Oranger',
        description: 'Crème dessert orientale à la fleur d\'oranger, pistaches concassées, sirop de rose',
        prices: {
          'Portion': 280,
          'Grand Format': 420
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Fondant au Chocolat',
        description: 'Cœur coulant au chocolat noir 70%, glace vanille, coulis de fruits rouges',
        prices: {
          'Portion': 420
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Salade de Fruits Exotiques',
        description: 'Mélange de fruits frais de saison: mangue, ananas, kiwi, fraises, menthe fraîche',
        prices: {
          'Portion': 320,
          'Grande Portion': 480
        },
        color: PRESET_COLORS[2]
      }
    ]
  },
  {
    id: createId(),
    name: 'Cafés & Boissons Chaudes',
    emoji: '☕',
    color: PRESET_COLORS[5] || PRESET_COLORS[0],
    items: [
      {
        id: createId(),
        name: 'Expresso Italien',
        description: 'Café expresso authentique, grains arabica torréfiés artisanalement',
        prices: {
          'Simple': 120,
          'Double': 180
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Cappuccino Crémeux',
        description: 'Expresso, mousse de lait onctuese, poudre de cacao, art latté',
        prices: {
          'Classique': 200,
          'Grand': 280
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Café Turc Traditionnel',
        description: 'Café moulu fin, préparé dans le sable chaud, servi avec loukoum',
        prices: {
          'Portion': 150
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Thé aux Amandes',
        description: 'Thé noir parfumé aux amandes, lait, miel, cannelle',
        prices: {
          'Théière': 180
        },
        color: PRESET_COLORS[3]
      },
      {
        id: createId(),
        name: 'Chocolat Chaud Maison',
        description: 'Chocolat noir 70% fondu, lait entier, chantilly, copeaux de chocolat',
        prices: {
          'Classique': 220,
          'épicé à la cannelle': 250
        },
        color: PRESET_COLORS[4]
      }
    ]
  }
];
