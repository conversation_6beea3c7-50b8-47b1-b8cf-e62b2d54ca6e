// knowledge: universal v4 seed for suppliers, inventory, menu, sub-recipes, and menu item recipes
import { addSupplier } from '../db/v4/operations/supplier-ops';
import { addStockItem } from '../db/v4/operations/inventory-ops';
import { addCategory, updateMenu, getMenu } from '../db/v4/operations/menu-ops';
import { createSubRecipe } from '../db/v4/operations/sub-recipe-ops';
import { createMenuItemRecipe } from '../db/v4/operations/menu-item-recipe-ops';
import { createSupplement, updateCategorySupplementConfig } from '../db/v4/operations/supplement-ops';
import { updateCategoryPackaging } from '../db/v4/operations/packaging-ops';
import { ensureDefaultSettings } from '../db/v4/operations/settings-ops';
import { RestaurantSettings } from '../db/v4/schemas/restaurant-settings-schema';
import { Supplement, MenuCategory } from '../db/v4/schemas/menu-schema';
import { getRandomPresetColor, PRESET_COLORS } from '../constants';
import { algerianMenu } from './algerian-menu-seed';
import { v4 as uuidv4 } from 'uuid';

// Minimal, real, and consistent data for a pizza restaurant
export async function runUniversalSeedV4() {
  // knowledge: wrap all in try/catch for safety
  try {
    console.log('🚀 Starting Universal Seed V4...');

    // --- RESTAURANT SETTINGS ---
    console.log('⏳ Ensuring default restaurant settings...');
    await ensureDefaultSettings();
    console.log('✅ Default restaurant settings ensured.');

    // --- SUPPLIERS ---
    console.log('⏳ Seeding suppliers...');
    const now = new Date().toISOString();
    const suppliers = [
      {
        name: 'Boulangerie Omar',
        phoneNumber: '0550123456',
        category: 'Boulangerie',
        notes: 'Pain traditionnel, semoule et farines',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Fromagerie Djurdjura',
        phoneNumber: '0550998877',
        category: 'Produits laitiers',
        notes: 'Fromages locaux et importés',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Grossiste Boissons El Bahdja',
        phoneNumber: '0771987654',
        category: 'Boissons',
        notes: 'Boissons gazeuses et eaux minérales',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Épicerie Atlas',
        phoneNumber: '0661234567',
        category: 'Épices et condiments',
        notes: 'Épices traditionnelles algériennes et orientales',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Boucherie Halal El Baraka',
        phoneNumber: '0772345678',
        category: 'Viandes',
        notes: 'Viandes fraîches halal de qualité',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Marché des Légumes Frais',
        phoneNumber: '0553456789',
        category: 'Légumes et fruits',
        notes: 'Légumes et fruits frais du marché local',
        balance: 0,
        isActive: true,
      },
    ];
    
    const supplierIds: Record<string, string> = {};
    for (const s of suppliers) {
      const created = await addSupplier(s);
      supplierIds[s.name] = created.id;
    }
    console.log('✅ Suppliers seeded.');

    // --- MINIMAL PIZZERIA INVENTORY ---
    console.log('⏳ Seeding minimal pizzeria inventory...');
    const stockItems = [
      // === PIZZA ESSENTIALS ===
      {
        name: 'Farine',
        category: 'Ingrédients',
        unit: 'kg' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 50,
        minLevel: 10,
        costPerUnit: 120,
      },
      {
        name: 'Levure',
        category: 'Ingrédients',
        unit: 'kg' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 2,
        minLevel: 0.5,
        costPerUnit: 800,
      },
      {
        name: 'Sel',
        category: 'Ingrédients',
        unit: 'kg' as const,
        supplierId: supplierIds['Épicerie Atlas'],
        quantity: 5,
        minLevel: 1,
        costPerUnit: 60,
      },
      {
        name: 'Huile',
        category: 'Ingrédients',
        unit: 'L' as const,
        supplierId: supplierIds['Épicerie Atlas'],
        quantity: 10,
        minLevel: 2,
        costPerUnit: 400,
      },
      {
        name: 'Mozzarella',
        category: 'Fromages',
        unit: 'kg' as const,
        supplierId: supplierIds['Fromagerie Djurdjura'],
        quantity: 25,
        minLevel: 5,
        costPerUnit: 800,
      },
      {
        name: 'Emmental',
        category: 'Fromages',
        unit: 'kg' as const,
        supplierId: supplierIds['Fromagerie Djurdjura'],
        quantity: 15,
        minLevel: 3,
        costPerUnit: 750,
      },
      {
        name: 'Tomates',
        category: 'Légumes',
        unit: 'kg' as const,
        supplierId: supplierIds['Marché des Légumes Frais'],
        quantity: 20,
        minLevel: 5,
        costPerUnit: 200,
      },
      {
        name: 'Champignons',
        category: 'Légumes',
        unit: 'kg' as const,
        supplierId: supplierIds['Marché des Légumes Frais'],
        quantity: 10,
        minLevel: 2,
        costPerUnit: 400,
      },
      {
        name: 'Poulet',
        category: 'Viandes',
        unit: 'kg' as const,
        supplierId: supplierIds['Boucherie Halal El Baraka'],
        quantity: 15,
        minLevel: 3,
        costPerUnit: 1200,
      },
      {
        name: 'Pepperoni',
        category: 'Viandes',
        unit: 'kg' as const,
        supplierId: supplierIds['Boucherie Halal El Baraka'],
        quantity: 8,
        minLevel: 2,
        costPerUnit: 1400,
      },

      // === BOISSONS STOCK (exact match with menu) ===
      {
        name: 'Coca 33cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 48,
        minLevel: 12,
        costPerUnit: 25,
      },
      {
        name: 'Coca 50cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 24,
        minLevel: 6,
        costPerUnit: 40,
      },
      {
        name: 'Coca 1.5L',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 12,
        minLevel: 3,
        costPerUnit: 75,
      },
      {
        name: 'Pepsi 33cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 48,
        minLevel: 12,
        costPerUnit: 25,
      },
      {
        name: 'Pepsi 50cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 24,
        minLevel: 6,
        costPerUnit: 40,
      },
      {
        name: 'Pepsi 1.5L',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 12,
        minLevel: 3,
        costPerUnit: 75,
      },
      {
        name: 'Fanta 33cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 48,
        minLevel: 12,
        costPerUnit: 22,
      },
      {
        name: 'Fanta 50cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 24,
        minLevel: 6,
        costPerUnit: 37,
      },
      {
        name: 'Fanta 1.5L',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 12,
        minLevel: 3,
        costPerUnit: 70,
      },
      {
        name: 'Sprite 33cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 48,
        minLevel: 12,
        costPerUnit: 22,
      },
      {
        name: 'Sprite 50cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 24,
        minLevel: 6,
        costPerUnit: 37,
      },
      {
        name: 'Sprite 1.5L',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 12,
        minLevel: 3,
        costPerUnit: 70,
      },
      {
        name: 'Eau 33cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 96,
        minLevel: 24,
        costPerUnit: 15,
      },
      {
        name: 'Eau 50cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 48,
        minLevel: 12,
        costPerUnit: 25,
      },
      {
        name: 'Eau 1.5L',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 24,
        minLevel: 6,
        costPerUnit: 50,
      },
      {
        name: 'Jus Orange 33cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Marché des Légumes Frais'],
        quantity: 24,
        minLevel: 6,
        costPerUnit: 40,
      },
      {
        name: 'Jus Orange 50cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Marché des Légumes Frais'],
        quantity: 12,
        minLevel: 3,
        costPerUnit: 65,
      },
      {
        name: 'Jus Orange 1.5L',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Marché des Légumes Frais'],
        quantity: 6,
        minLevel: 2,
        costPerUnit: 125,
      },

      // === PACKAGING ===
      {
        name: 'Boîtes Pizza',
        category: 'Emballages',
        unit: 'pcs' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 200,
        minLevel: 50,
        costPerUnit: 25,
      },
    ];
    
    const stockItemIds: Record<string, string> = {};
    for (const s of stockItems) {
      const created = await addStockItem(s);
      stockItemIds[s.name] = created.id;
    }
    console.log('✅ Inventory items seeded.');

    // --- RECETTES PIZZERIA ---
    console.log('⏳ Seeding pizza recipes...');
    const subRecipes = [
      {
        name: 'Pâte à Pizza',
        ingredients: [
          { stockItemId: stockItemIds['Farine 00 Italienne'], quantity: 1 },
          { stockItemId: stockItemIds['Levure Boulangère Fraîche'], quantity: 0.02 },
          { stockItemId: stockItemIds['Sel de Mer Fin'], quantity: 0.02 },
          { stockItemId: stockItemIds["Huile d'Olive Extra Vierge"], quantity: 0.05 },
        ],
        costPerUnit: 0,
        unit: 'kg' as const,
        yield: { quantity: 1.2, unit: 'kg' as const },
      },
      {
        name: 'Base Pizza Margherita',
        ingredients: [
          { stockItemId: stockItemIds['Sauce Tomate San Marzano'], quantity: 0.15 },
          { stockItemId: stockItemIds['Mozzarella di Bufala Râpée'], quantity: 0.2 },
        ],
        costPerUnit: 0,
        unit: 'kg' as const,
        yield: { quantity: 0.35, unit: 'kg' as const },
      },
    ];
    
    const subRecipeIds: Record<string, string> = {};
    for (const sr of subRecipes) {
      const created = await createSubRecipe(sr);
      subRecipeIds[sr.name] = created._id;
    }
    console.log('✅ Sub-recipes seeded.');

    // --- MINIMAL PIZZERIA MENU ---
    console.log('⏳ Seeding minimal pizzeria menu...');
    
    const menuCategoryIds: Record<string, string> = {};
    for (const category of algerianMenu) {
      await addCategory(category as MenuCategory);
      menuCategoryIds[category.name] = category.id;
      console.log(`✅ Category '${category.name}' with ${category.items.length} items created.`);
    }

    console.log('✅ Minimal pizzeria menu seeded successfully!');
    console.log(`📊 Total categories: ${algerianMenu.length}`);
    console.log(`📊 Total items: ${algerianMenu.reduce((total, cat) => total + cat.items.length, 0)}`);

    // --- MENU ITEM RECIPES ---
    console.log('⏳ Seeding menu item recipes...');
    
    // Find categories
    const pizzasCategory = algerianMenu.find(cat => cat.name === 'Pizzas');
    const boissonsCategory = algerianMenu.find(cat => cat.name === 'Boissons');
    
    // Pizza Margherita recipe
    const margheritaItem = pizzasCategory?.items.find(item => item.name === 'Margherita');
    if (margheritaItem) {
      await createMenuItemRecipe({
        menuItemId: margheritaItem.id,
        ingredients: [
          { subRecipeId: subRecipeIds['Pâte à Pizza'], quantity: 0.3 },
          { subRecipeId: subRecipeIds['Base Pizza Margherita'], quantity: 1 },
        ],
      });
    }

    // Drink recipes - each drink consumes exactly 1 piece of corresponding stock
    if (boissonsCategory) {
      for (const drinkItem of boissonsCategory.items) {
        // Create recipes for each size
        for (const size of Object.keys(drinkItem.prices)) {
          const stockItemName = `${drinkItem.name} ${size}`;
          const stockItemId = stockItemIds[stockItemName];
          
          if (stockItemId) {
            await createMenuItemRecipe({
              menuItemId: drinkItem.id,
              sizeVariant: size,
              ingredients: [
                { stockItemId: stockItemId, quantity: 1 },
              ],
            });
          }
        }
      }
    }

    console.log('✅ Menu item recipes seeded.');

    // --- PACKAGING CONFIGURATION ---
    console.log('⏳ Configuring packaging rules...');
    
    // Configure packaging for Pizza category
    const pizzaCategory = algerianMenu.find(cat => cat.name === 'Pizzas');
    
    if (pizzaCategory) {
      const pizzaPackagingRules = {
        'Normale': {
          'takeaway': [
            { stockItemId: stockItemIds['Boîtes Pizza'], quantity: 1 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Boîtes Pizza'], quantity: 1 }
          ]
        },
        'Mega': {
          'takeaway': [
            { stockItemId: stockItemIds['Boîtes Pizza'], quantity: 1 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Boîtes Pizza'], quantity: 1 }
          ]
        },
        'Géante': {
          'takeaway': [
            { stockItemId: stockItemIds['Boîtes Pizza'], quantity: 1 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Boîtes Pizza'], quantity: 1 }
          ]
        }
      };

      // Apply pizza packaging rules
      for (const [size, orderTypes] of Object.entries(pizzaPackagingRules)) {
        for (const [orderType, packaging] of Object.entries(orderTypes)) {
          await updateCategoryPackaging(pizzaCategory.id, size, orderType as any, packaging);
        }
      }
    }
    console.log('✅ Packaging rules configured.');

    // --- CATEGORY-SPECIFIC SUPPLEMENTS ---
    console.log('⏳ Seeding category-specific supplements...');
    
    // Pizza supplements
    const pizzaSupplements: Supplement[] = [
      {
        id: `sup_${uuidv4()}`,
        name: 'Extra Fromage',
        description: 'Double portion de fromage',
        stockConsumption: {
          stockItemId: stockItemIds['Mozzarella'],
          quantities: { Normale: 0.05, Mega: 0.08, Géante: 0.1 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Champignons',
        description: 'Ajout de champignons',
        stockConsumption: {
          stockItemId: stockItemIds['Champignons'],
          quantities: { Normale: 0.03, Mega: 0.05, Géante: 0.07 },
        },
        isActive: true,
      },
    ];

    // Create supplements for Pizza category
    const pizzaCategoryId = menuCategoryIds['Pizzas'];
    
    if (pizzaCategoryId) {
      for (const supplement of pizzaSupplements) {
        await createSupplement(pizzaCategoryId, supplement);
      }
      
      // Configure pricing for Pizza supplements
      await updateCategorySupplementConfig(pizzaCategoryId, {
        globalPricing: { Normale: 50, Mega: 75, Géante: 100 },
        isEnabled: true,
      });
    }

    console.log('✅ Category-specific supplements seeded and configured.');

    console.log('🎉 Universal seed V4 completed successfully!');
  } catch (error) {
    console.error('❌ Error running universal seed V4:', error);
    throw error;
  }
}
